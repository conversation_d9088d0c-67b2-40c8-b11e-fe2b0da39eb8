import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/components/modern_bottom_nav.dart';
import 'package:myapp/screens/location_therapist_finder.dart';

class UserAppointmentsScreen extends StatefulWidget {
  final String userId;

  const UserAppointmentsScreen({
    Key? key,
    required this.userId,
  }) : super(key: key);

  @override
  _UserAppointmentsScreenState createState() => _UserAppointmentsScreenState();
}

class _UserAppointmentsScreenState extends State<UserAppointmentsScreen> {
  List<Map<String, dynamic>> appointments = [];
  List<Map<String, dynamic>> upcomingAppointments = [];
  List<Map<String, dynamic>> pastAppointments = [];
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _fetchUserAppointments();
  }

  Future<void> _fetchUserAppointments() async {
    try {
      final response = await http.get(
        Uri.parse(
            'http://192.168.1.9:3000/api/appointments/user/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> appointmentData = jsonDecode(response.body);
        final now = DateTime.now();

        setState(() {
          appointments = appointmentData.cast<Map<String, dynamic>>();

          // Separate upcoming and past appointments
          upcomingAppointments = appointments.where((appointment) {
            final appointmentDate =
                DateTime.parse(appointment['appointmentDate']);
            return appointmentDate.isAfter(now);
          }).toList();

          pastAppointments = appointments.where((appointment) {
            final appointmentDate =
                DateTime.parse(appointment['appointmentDate']);
            return appointmentDate.isBefore(now);
          }).toList();

          // Sort upcoming appointments by date (earliest first)
          upcomingAppointments.sort((a, b) {
            final dateA = DateTime.parse(a['appointmentDate']);
            final dateB = DateTime.parse(b['appointmentDate']);
            return dateA.compareTo(dateB);
          });

          // Sort past appointments by date (most recent first)
          pastAppointments.sort((a, b) {
            final dateA = DateTime.parse(a['appointmentDate']);
            final dateB = DateTime.parse(b['appointmentDate']);
            return dateB.compareTo(dateA);
          });

          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = 'Failed to load appointments';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'An error occurred: $e';
        isLoading = false;
      });
    }
  }

  Future<void> _launchMeetingLink(String meetingLink) async {
    try {
      final Uri url = Uri.parse(meetingLink);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not launch meeting link')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error launching meeting link: $e')),
      );
    }
  }

  void _copyMeetingLink(String meetingLink) {
    Clipboard.setData(ClipboardData(text: meetingLink));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Meeting link copied to clipboard!')),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      case 'rescheduled':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return Icons.schedule;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      case 'rescheduled':
        return Icons.update;
      default:
        return Icons.help;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenWithBottomNav(
      currentIndex: 1, // Appointments is index 1 in the bottom nav
      userId: widget.userId,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          ),
          title: Text(
            "Appointments",
            style: AppTheme.headingMedium.copyWith(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
        ),
        body: isLoading
            ? const Center(child: CircularProgressIndicator())
            : errorMessage != null
                ? Center(
                    child: Text(
                      errorMessage!,
                      style: AppTheme.bodyLarge
                          .copyWith(color: AppTheme.errorColor),
                    ),
                  )
                : Column(
                    children: [
                      // Upcoming Section
                      Padding(
                        padding: const EdgeInsets.all(AppTheme.spacingM),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Upcoming',
                              style: AppTheme.headingMedium.copyWith(
                                color: AppTheme.textPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: AppTheme.spacingM),
                            ...upcomingAppointments.map((appointment) =>
                                _buildAppointmentCard(appointment,
                                    isUpcoming: true)),
                          ],
                        ),
                      ),
                      // Past Section
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(AppTheme.spacingM),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Past',
                                style: AppTheme.headingMedium.copyWith(
                                  color: AppTheme.textPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: AppTheme.spacingM),
                              Expanded(
                                child: ListView.builder(
                                  itemCount: pastAppointments.length,
                                  itemBuilder: (context, index) {
                                    return _buildAppointmentCard(
                                      pastAppointments[index],
                                      isUpcoming: false,
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    LocationTherapistFinder(userId: widget.userId),
              ),
            );
          },
          backgroundColor: AppTheme.primaryColor,
          icon: const Icon(Icons.add, color: Colors.white),
          label: const Text(
            'New Appointment',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  Widget _buildAppointmentCard(Map<String, dynamic> appointment,
      {required bool isUpcoming}) {
    final DateTime appointmentDate =
        DateTime.parse(appointment['appointmentDate']);
    final therapist = appointment['therapist'] ?? {};
    final String therapistName = therapist['name'] ?? 'Dr. Emily Carter';
    final String therapistImage = therapist['profileImage'] ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Therapist Profile Image
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              color: AppTheme.primaryColor.withOpacity(0.1),
            ),
            child: therapistImage.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(25),
                    child: Image.network(
                      therapistImage,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.person,
                          color: AppTheme.primaryColor,
                          size: 30,
                        );
                      },
                    ),
                  )
                : Icon(
                    Icons.person,
                    color: AppTheme.primaryColor,
                    size: 30,
                  ),
          ),
          const SizedBox(width: AppTheme.spacingM),
          // Appointment Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Video Call',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  therapistName,
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          // Date and Time
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                isUpcoming ? 'Today, 10:00 AM' : 'Yesterday, 11:00 AM',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              if (isUpcoming && appointment['meetingLink'] != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: ElevatedButton(
                    onPressed: () => _joinVideoCall(appointment),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      minimumSize: Size.zero,
                    ),
                    child: const Text(
                      'Join',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  void _joinVideoCall(Map<String, dynamic> appointment) {
    final meetingLink = appointment['meetingLink'];
    final therapistName = appointment['therapist']?['name'] ?? 'Therapist';

    if (meetingLink != null) {
      Navigator.pushNamed(
        context,
        '/video_call',
        arguments: {
          'meetingLink': meetingLink,
          'therapistName': therapistName,
          'userId': widget.userId,
        },
      );
    }
  }
}
