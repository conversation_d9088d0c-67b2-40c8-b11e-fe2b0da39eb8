import 'package:flutter/foundation.dart';
import '../models/assessment_models.dart';

class TherapistReferralService {
  static final TherapistReferralService _instance = TherapistReferralService._internal();
  factory TherapistReferralService() => _instance;
  TherapistReferralService._internal();

  /// Get therapist recommendations based on assessment results
  List<TherapistReferral> getTherapistRecommendations(AssessmentResult assessmentResult) {
    final List<TherapistReferral> recommendations = [];

    // Add specialized therapists based on primary condition
    switch (assessmentResult.overallResult.primaryCondition) {
      case 'anxiety':
        recommendations.addAll(_getAnxietySpecialists());
        break;
      case 'depression':
        recommendations.addAll(_getDepressionSpecialists());
        break;
      case 'ptsd':
        recommendations.addAll(_getPTSDSpecialists());
        break;
      case 'bipolar':
        recommendations.addAll(_getBipolarSpecialists());
        break;
      case 'ocd':
        recommendations.addAll(_getOCDSpecialists());
        break;
      default:
        recommendations.addAll(_getGeneralTherapists());
    }

    // Add emergency contacts if high risk
    if (assessmentResult.riskLevel == 'critical' || assessmentResult.overallResult.needsImmediateAttention) {
      recommendations.insertAll(0, _getEmergencyContacts());
    }

    return recommendations;
  }

  /// Get emergency resources and contacts
  List<EmergencyResource> getEmergencyResources() {
    return [
      EmergencyResource(
        name: 'National Suicide Prevention Lifeline',
        phoneNumber: '988',
        description: '24/7 free and confidential support for people in distress',
        isAvailable24_7: true,
        type: 'crisis',
      ),
      EmergencyResource(
        name: 'Crisis Text Line',
        phoneNumber: '741741',
        description: 'Text HOME to 741741 for 24/7 crisis support',
        isAvailable24_7: true,
        type: 'crisis',
      ),
      EmergencyResource(
        name: 'Emergency Services',
        phoneNumber: '911',
        description: 'Call 911 for immediate emergency assistance',
        isAvailable24_7: true,
        type: 'emergency',
      ),
      EmergencyResource(
        name: 'SAMHSA National Helpline',
        phoneNumber: '**************',
        description: 'Treatment referral and information service',
        isAvailable24_7: true,
        type: 'hotline',
      ),
      EmergencyResource(
        name: 'Veterans Crisis Line',
        phoneNumber: '**************',
        description: 'Support for veterans and their families',
        isAvailable24_7: true,
        type: 'crisis',
      ),
      EmergencyResource(
        name: 'LGBTQ National Hotline',
        phoneNumber: '**************',
        description: 'Support for LGBTQ individuals',
        isAvailable24_7: false,
        type: 'hotline',
      ),
    ];
  }

  /// Get coping strategies based on assessment results
  List<String> getCopingStrategies(AssessmentResult assessmentResult) {
    final List<String> strategies = [];

    // Add immediate coping strategies based on risk level
    if (assessmentResult.riskLevel == 'critical') {
      strategies.addAll(_getCriticalCopingStrategies());
    } else if (assessmentResult.riskLevel == 'high') {
      strategies.addAll(_getHighRiskCopingStrategies());
    }

    // Add condition-specific strategies
    switch (assessmentResult.overallResult.primaryCondition) {
      case 'anxiety':
        strategies.addAll(_getAnxietyCopingStrategies());
        break;
      case 'depression':
        strategies.addAll(_getDepressionCopingStrategies());
        break;
      case 'stress':
        strategies.addAll(_getStressCopingStrategies());
        break;
      case 'ptsd':
        strategies.addAll(_getPTSDCopingStrategies());
        break;
    }

    // Add general wellness strategies
    strategies.addAll(_getGeneralWellnessStrategies());

    return strategies.take(10).toList(); // Limit to top 10 strategies
  }

  /// Private helper methods for different specialist types
  List<TherapistReferral> _getAnxietySpecialists() {
    return [
      TherapistReferral(
        name: 'Dr. Sarah Johnson, PhD',
        specialization: 'Anxiety Disorders & Cognitive Behavioral Therapy',
        contactInfo: 'Phone: (************* | Email: <EMAIL>',
        location: 'Downtown Mental Health Center',
        isEmergency: false,
        availability: 'Mon-Fri 9AM-6PM',
      ),
      TherapistReferral(
        name: 'Dr. Michael Chen, PsyD',
        specialization: 'Panic Disorder & Exposure Therapy',
        contactInfo: 'Phone: (************* | Email: <EMAIL>',
        location: 'Anxiety Treatment Clinic',
        isEmergency: false,
        availability: 'Tue-Sat 10AM-7PM',
      ),
    ];
  }

  List<TherapistReferral> _getDepressionSpecialists() {
    return [
      TherapistReferral(
        name: 'Dr. Emily Rodriguez, MD',
        specialization: 'Depression & Mood Disorders',
        contactInfo: 'Phone: (************* | Email: <EMAIL>',
        location: 'Comprehensive Mood Center',
        isEmergency: false,
        availability: 'Mon-Thu 8AM-5PM',
      ),
      TherapistReferral(
        name: 'Dr. David Kim, LCSW',
        specialization: 'Depression & Interpersonal Therapy',
        contactInfo: 'Phone: (************* | Email: <EMAIL>',
        location: 'Community Mental Health Services',
        isEmergency: false,
        availability: 'Mon-Fri 9AM-6PM',
      ),
    ];
  }

  List<TherapistReferral> _getPTSDSpecialists() {
    return [
      TherapistReferral(
        name: 'Dr. Lisa Thompson, PhD',
        specialization: 'PTSD & Trauma-Focused Therapy',
        contactInfo: 'Phone: (************* | Email: <EMAIL>',
        location: 'Trauma Recovery Center',
        isEmergency: false,
        availability: 'Mon-Fri 8AM-6PM',
      ),
    ];
  }

  List<TherapistReferral> _getBipolarSpecialists() {
    return [
      TherapistReferral(
        name: 'Dr. Robert Wilson, MD',
        specialization: 'Bipolar Disorder & Mood Stabilization',
        contactInfo: 'Phone: (************* | Email: <EMAIL>',
        location: 'Mood Disorders Institute',
        isEmergency: false,
        availability: 'Tue-Sat 9AM-5PM',
      ),
    ];
  }

  List<TherapistReferral> _getOCDSpecialists() {
    return [
      TherapistReferral(
        name: 'Dr. Amanda Foster, PhD',
        specialization: 'OCD & Exposure Response Prevention',
        contactInfo: 'Phone: (************* | Email: <EMAIL>',
        location: 'OCD Treatment Center',
        isEmergency: false,
        availability: 'Mon-Fri 10AM-7PM',
      ),
    ];
  }

  List<TherapistReferral> _getGeneralTherapists() {
    return [
      TherapistReferral(
        name: 'Dr. Jennifer Martinez, LMFT',
        specialization: 'General Mental Health & Counseling',
        contactInfo: 'Phone: (************* | Email: <EMAIL>',
        location: 'General Counseling Services',
        isEmergency: false,
        availability: 'Mon-Fri 9AM-6PM',
      ),
    ];
  }

  List<TherapistReferral> _getEmergencyContacts() {
    return [
      TherapistReferral(
        name: 'Crisis Intervention Team',
        specialization: 'Emergency Mental Health Crisis Response',
        contactInfo: 'Phone: 911 or (555) CRISIS-1',
        location: 'Available citywide',
        isEmergency: true,
        availability: '24/7 Emergency Response',
      ),
      TherapistReferral(
        name: 'Mobile Crisis Unit',
        specialization: 'On-site Crisis Assessment & Intervention',
        contactInfo: 'Phone: (555) 123-HELP',
        location: 'Mobile service - comes to you',
        isEmergency: true,
        availability: '24/7 Mobile Response',
      ),
    ];
  }

  /// Coping strategies by risk level and condition
  List<String> _getCriticalCopingStrategies() {
    return [
      'Call 988 (Suicide Prevention Lifeline) immediately if having thoughts of self-harm',
      'Go to the nearest emergency room if in immediate danger',
      'Call 911 for emergency assistance',
      'Reach out to a trusted friend or family member right now',
      'Remove any means of self-harm from your immediate environment',
      'Stay with someone you trust until professional help arrives',
    ];
  }

  List<String> _getHighRiskCopingStrategies() {
    return [
      'Contact a mental health professional within 24 hours',
      'Reach out to your support network immediately',
      'Use grounding techniques: 5 things you see, 4 you hear, 3 you touch, 2 you smell, 1 you taste',
      'Practice deep breathing: 4 counts in, hold for 4, out for 6',
      'Avoid alcohol and drugs which can worsen symptoms',
      'Stay in safe, supportive environments',
    ];
  }

  List<String> _getAnxietyCopingStrategies() {
    return [
      'Practice the 4-7-8 breathing technique',
      'Use progressive muscle relaxation',
      'Try mindfulness meditation for 10 minutes daily',
      'Challenge anxious thoughts with evidence-based thinking',
      'Limit caffeine and stimulants',
      'Engage in regular physical exercise',
      'Create a daily routine to provide structure',
    ];
  }

  List<String> _getDepressionCopingStrategies() {
    return [
      'Maintain a regular sleep schedule (7-9 hours nightly)',
      'Engage in at least 30 minutes of physical activity daily',
      'Practice gratitude by writing down 3 positive things daily',
      'Stay connected with supportive friends and family',
      'Set small, achievable daily goals',
      'Spend time outdoors and in natural light',
      'Consider joining a support group',
    ];
  }

  List<String> _getStressCopingStrategies() {
    return [
      'Break large tasks into smaller, manageable steps',
      'Practice time management and prioritization',
      'Set healthy boundaries with work and relationships',
      'Take regular breaks throughout the day',
      'Practice saying "no" to additional commitments',
      'Use relaxation techniques like yoga or tai chi',
      'Maintain work-life balance',
    ];
  }

  List<String> _getPTSDCopingStrategies() {
    return [
      'Practice grounding techniques when experiencing flashbacks',
      'Create a safe space in your home for relaxation',
      'Use bilateral stimulation (alternating tapping) during distress',
      'Maintain a trauma journal to track triggers and progress',
      'Avoid alcohol and drugs as coping mechanisms',
      'Consider EMDR or trauma-focused therapy',
      'Build a strong support network of understanding people',
    ];
  }

  List<String> _getGeneralWellnessStrategies() {
    return [
      'Maintain a balanced diet with regular meals',
      'Stay hydrated by drinking plenty of water',
      'Practice good sleep hygiene',
      'Engage in hobbies and activities you enjoy',
      'Limit social media and news consumption if overwhelming',
      'Practice self-compassion and avoid self-criticism',
      'Consider keeping a mood or wellness journal',
    ];
  }

  /// Check if immediate professional help is needed
  bool requiresImmediateProfessionalHelp(AssessmentResult assessmentResult) {
    return assessmentResult.riskLevel == 'critical' || 
           assessmentResult.overallResult.needsImmediateAttention ||
           assessmentResult.requiresProfessionalHelp;
  }

  /// Get appropriate next steps based on assessment
  List<String> getNextSteps(AssessmentResult assessmentResult) {
    final List<String> steps = [];

    if (requiresImmediateProfessionalHelp(assessmentResult)) {
      steps.addAll([
        'Contact a mental health professional immediately',
        'Consider emergency services if in crisis',
        'Inform a trusted person about your current state',
        'Follow up with your primary care physician',
      ]);
    } else if (assessmentResult.riskLevel == 'high') {
      steps.addAll([
        'Schedule an appointment with a mental health professional within the week',
        'Implement immediate coping strategies',
        'Monitor symptoms daily',
        'Build your support network',
      ]);
    } else {
      steps.addAll([
        'Consider scheduling a consultation with a therapist',
        'Implement recommended coping strategies',
        'Monitor your mental health regularly',
        'Practice self-care activities',
      ]);
    }

    return steps;
  }
}
