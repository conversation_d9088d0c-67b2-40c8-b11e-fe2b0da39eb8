// Mental Health Assessment Models
// Comprehensive data models for the mental health assessment system

class AssessmentQuestion {
  final String id;
  final String question;
  final String category;
  final List<AssessmentOption> options;
  final int weight;
  final String? description;

  AssessmentQuestion({
    required this.id,
    required this.question,
    required this.category,
    required this.options,
    this.weight = 1,
    this.description,
  });
}

class AssessmentOption {
  final String id;
  final String text;
  final int score;
  final String severity;

  AssessmentOption({
    required this.id,
    required this.text,
    required this.score,
    required this.severity,
  });
}

class AssessmentResponse {
  final String questionId;
  final String optionId;
  final int score;
  final String category;
  final DateTime timestamp;

  AssessmentResponse({
    required this.questionId,
    required this.optionId,
    required this.score,
    required this.category,
    required this.timestamp,
  });
}

class AssessmentResult {
  final String id;
  final DateTime completedAt;
  final Map<String, CategoryResult> categoryResults;
  final OverallResult overallResult;
  final List<String> recommendations;
  final List<String> resources;
  final bool requiresProfessionalHelp;
  final String riskLevel;
  final double confidenceScore;
  final Map<String, dynamic> aiAnalysis;

  AssessmentResult({
    required this.id,
    required this.completedAt,
    required this.categoryResults,
    required this.overallResult,
    required this.recommendations,
    required this.resources,
    required this.requiresProfessionalHelp,
    required this.riskLevel,
    required this.confidenceScore,
    required this.aiAnalysis,
  });
}

class CategoryResult {
  final String category;
  final int totalScore;
  final int maxScore;
  final double percentage;
  final String severity;
  final String description;
  final List<String> symptoms;
  final List<String> recommendations;

  CategoryResult({
    required this.category,
    required this.totalScore,
    required this.maxScore,
    required this.percentage,
    required this.severity,
    required this.description,
    required this.symptoms,
    required this.recommendations,
  });
}

class OverallResult {
  final String primaryCondition;
  final String secondaryCondition;
  final String overallSeverity;
  final String summary;
  final List<String> keyFindings;
  final bool needsImmediateAttention;

  OverallResult({
    required this.primaryCondition,
    required this.secondaryCondition,
    required this.overallSeverity,
    required this.summary,
    required this.keyFindings,
    required this.needsImmediateAttention,
  });
}

class TherapistReferral {
  final String name;
  final String specialization;
  final String contactInfo;
  final String location;
  final bool isEmergency;
  final String availability;

  TherapistReferral({
    required this.name,
    required this.specialization,
    required this.contactInfo,
    required this.location,
    required this.isEmergency,
    required this.availability,
  });
}

class EmergencyResource {
  final String name;
  final String phoneNumber;
  final String description;
  final bool isAvailable24_7;
  final String type; // 'hotline', 'emergency', 'crisis'

  EmergencyResource({
    required this.name,
    required this.phoneNumber,
    required this.description,
    required this.isAvailable24_7,
    required this.type,
  });
}

class AssessmentHistory {
  final String userId;
  final List<AssessmentResult> results;
  final DateTime lastAssessment;
  final Map<String, List<double>> progressTracking;

  AssessmentHistory({
    required this.userId,
    required this.results,
    required this.lastAssessment,
    required this.progressTracking,
  });
}

// Assessment Categories
enum AssessmentCategory {
  anxiety,
  depression,
  stress,
  ptsd,
  bipolar,
  ocd,
  generalWellbeing,
}

extension AssessmentCategoryExtension on AssessmentCategory {
  String get displayName {
    switch (this) {
      case AssessmentCategory.anxiety:
        return 'Anxiety Disorders';
      case AssessmentCategory.depression:
        return 'Depression';
      case AssessmentCategory.stress:
        return 'Stress & Burnout';
      case AssessmentCategory.ptsd:
        return 'PTSD & Trauma';
      case AssessmentCategory.bipolar:
        return 'Bipolar Disorder';
      case AssessmentCategory.ocd:
        return 'OCD';
      case AssessmentCategory.generalWellbeing:
        return 'General Wellbeing';
    }
  }

  String get description {
    switch (this) {
      case AssessmentCategory.anxiety:
        return 'Assesses various anxiety disorders including generalized anxiety, panic disorder, and social anxiety';
      case AssessmentCategory.depression:
        return 'Evaluates symptoms of major depression and persistent depressive disorder';
      case AssessmentCategory.stress:
        return 'Measures stress levels, burnout, and stress-related symptoms';
      case AssessmentCategory.ptsd:
        return 'Screens for post-traumatic stress disorder and trauma-related symptoms';
      case AssessmentCategory.bipolar:
        return 'Identifies potential bipolar disorder indicators and mood swings';
      case AssessmentCategory.ocd:
        return 'Assesses obsessive-compulsive disorder symptoms and behaviors';
      case AssessmentCategory.generalWellbeing:
        return 'Overall mental health and wellbeing assessment';
    }
  }
}

// Severity Levels
enum SeverityLevel {
  minimal,
  mild,
  moderate,
  severe,
  critical,
}

extension SeverityLevelExtension on SeverityLevel {
  String get displayName {
    switch (this) {
      case SeverityLevel.minimal:
        return 'Minimal';
      case SeverityLevel.mild:
        return 'Mild';
      case SeverityLevel.moderate:
        return 'Moderate';
      case SeverityLevel.severe:
        return 'Severe';
      case SeverityLevel.critical:
        return 'Critical';
    }
  }

  String get description {
    switch (this) {
      case SeverityLevel.minimal:
        return 'Little to no symptoms present';
      case SeverityLevel.mild:
        return 'Some symptoms present but manageable';
      case SeverityLevel.moderate:
        return 'Noticeable symptoms affecting daily life';
      case SeverityLevel.severe:
        return 'Significant symptoms requiring attention';
      case SeverityLevel.critical:
        return 'Severe symptoms requiring immediate professional help';
    }
  }

  String get color {
    switch (this) {
      case SeverityLevel.minimal:
        return '#4CAF50'; // Green
      case SeverityLevel.mild:
        return '#8BC34A'; // Light Green
      case SeverityLevel.moderate:
        return '#FF9800'; // Orange
      case SeverityLevel.severe:
        return '#FF5722'; // Deep Orange
      case SeverityLevel.critical:
        return '#F44336'; // Red
    }
  }
}
