import '../models/assessment_models.dart';

class AssessmentQuestionsService {
  static final AssessmentQuestionsService _instance =
      AssessmentQuestionsService._internal();
  factory AssessmentQuestionsService() => _instance;
  AssessmentQuestionsService._internal();

  /// Get all assessment questions organized by category
  List<AssessmentQuestion> getAllQuestions() {
    return [
      ...getAnxietyQuestions(),
      ...getDepressionQuestions(),
      ...getStressQuestions(),
      ...getPTSDQuestions(),
      ...getBipolarQuestions(),
      ...getOCDQuestions(),
      ...getGeneralWellbeingQuestions(),
    ];
  }

  /// Anxiety Disorders Questions (GAD-7 inspired + additional)
  List<AssessmentQuestion> getAnxietyQuestions() {
    return [
      AssessmentQuestion(
        id: 'anxiety_1',
        category: 'anxiety',
        question:
            'Over the last 2 weeks, how often have you been bothered by feeling nervous, anxious, or on edge?',
        options: [
          AssessmentOption(
              id: 'a1_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'a1_2', text: 'Several days', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'a1_3',
              text: 'More than half the days',
              score: 2,
              severity: 'moderate'),
          AssessmentOption(
              id: 'a1_4',
              text: 'Nearly every day',
              score: 3,
              severity: 'severe'),
        ],
        weight: 2,
      ),
      AssessmentQuestion(
        id: 'anxiety_2',
        category: 'anxiety',
        question: 'How often have you been unable to stop or control worrying?',
        options: [
          AssessmentOption(
              id: 'a2_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'a2_2', text: 'Several days', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'a2_3',
              text: 'More than half the days',
              score: 2,
              severity: 'moderate'),
          AssessmentOption(
              id: 'a2_4',
              text: 'Nearly every day',
              score: 3,
              severity: 'severe'),
        ],
        weight: 2,
      ),
      AssessmentQuestion(
        id: 'anxiety_3',
        category: 'anxiety',
        question: 'How often do you worry too much about different things?',
        options: [
          AssessmentOption(
              id: 'a3_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'a3_2', text: 'Several days', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'a3_3',
              text: 'More than half the days',
              score: 2,
              severity: 'moderate'),
          AssessmentOption(
              id: 'a3_4',
              text: 'Nearly every day',
              score: 3,
              severity: 'severe'),
        ],
        weight: 2,
      ),
      AssessmentQuestion(
        id: 'anxiety_4',
        category: 'anxiety',
        question: 'How often do you have trouble relaxing?',
        options: [
          AssessmentOption(
              id: 'a4_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'a4_2', text: 'Several days', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'a4_3',
              text: 'More than half the days',
              score: 2,
              severity: 'moderate'),
          AssessmentOption(
              id: 'a4_4',
              text: 'Nearly every day',
              score: 3,
              severity: 'severe'),
        ],
        weight: 1,
      ),
      AssessmentQuestion(
        id: 'anxiety_5',
        category: 'anxiety',
        question: 'Do you experience sudden episodes of intense fear or panic?',
        options: [
          AssessmentOption(
              id: 'a5_1', text: 'Never', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'a5_2', text: 'Rarely', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'a5_3', text: 'Sometimes', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'a5_4', text: 'Frequently', score: 3, severity: 'severe'),
        ],
        weight: 3,
        description:
            'Panic attacks can include symptoms like rapid heartbeat, sweating, trembling, or feeling like you\'re going to die.',
      ),
      AssessmentQuestion(
        id: 'anxiety_6',
        category: 'anxiety',
        question:
            'How often do you avoid social situations due to fear or anxiety?',
        options: [
          AssessmentOption(
              id: 'a6_1', text: 'Never', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'a6_2', text: 'Rarely', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'a6_3', text: 'Sometimes', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'a6_4', text: 'Often', score: 3, severity: 'severe'),
        ],
        weight: 2,
      ),
    ];
  }

  /// Depression Questions (PHQ-9 inspired + additional)
  List<AssessmentQuestion> getDepressionQuestions() {
    return [
      AssessmentQuestion(
        id: 'depression_1',
        category: 'depression',
        question:
            'Over the last 2 weeks, how often have you had little interest or pleasure in doing things?',
        options: [
          AssessmentOption(
              id: 'd1_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'd1_2', text: 'Several days', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'd1_3',
              text: 'More than half the days',
              score: 2,
              severity: 'moderate'),
          AssessmentOption(
              id: 'd1_4',
              text: 'Nearly every day',
              score: 3,
              severity: 'severe'),
        ],
        weight: 3,
      ),
      AssessmentQuestion(
        id: 'depression_2',
        category: 'depression',
        question:
            'How often have you been feeling down, depressed, or hopeless?',
        options: [
          AssessmentOption(
              id: 'd2_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'd2_2', text: 'Several days', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'd2_3',
              text: 'More than half the days',
              score: 2,
              severity: 'moderate'),
          AssessmentOption(
              id: 'd2_4',
              text: 'Nearly every day',
              score: 3,
              severity: 'severe'),
        ],
        weight: 3,
      ),
      AssessmentQuestion(
        id: 'depression_3',
        category: 'depression',
        question:
            'How often do you have trouble falling or staying asleep, or sleeping too much?',
        options: [
          AssessmentOption(
              id: 'd3_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'd3_2', text: 'Several days', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'd3_3',
              text: 'More than half the days',
              score: 2,
              severity: 'moderate'),
          AssessmentOption(
              id: 'd3_4',
              text: 'Nearly every day',
              score: 3,
              severity: 'severe'),
        ],
        weight: 2,
      ),
      AssessmentQuestion(
        id: 'depression_4',
        category: 'depression',
        question: 'How often do you feel tired or have little energy?',
        options: [
          AssessmentOption(
              id: 'd4_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'd4_2', text: 'Several days', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'd4_3',
              text: 'More than half the days',
              score: 2,
              severity: 'moderate'),
          AssessmentOption(
              id: 'd4_4',
              text: 'Nearly every day',
              score: 3,
              severity: 'severe'),
        ],
        weight: 2,
      ),
      AssessmentQuestion(
        id: 'depression_5',
        category: 'depression',
        question:
            'Have you had thoughts that you would be better off dead or of hurting yourself?',
        options: [
          AssessmentOption(
              id: 'd5_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'd5_2', text: 'Several days', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'd5_3',
              text: 'More than half the days',
              score: 4,
              severity: 'severe'),
          AssessmentOption(
              id: 'd5_4',
              text: 'Nearly every day',
              score: 6,
              severity: 'critical'),
        ],
        weight: 5,
        description:
            'This is a critical question. Any positive response requires immediate professional attention.',
      ),
    ];
  }

  /// Stress and Burnout Questions
  List<AssessmentQuestion> getStressQuestions() {
    return [
      AssessmentQuestion(
        id: 'stress_1',
        category: 'stress',
        question: 'How often do you feel overwhelmed by your responsibilities?',
        options: [
          AssessmentOption(
              id: 's1_1', text: 'Never', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 's1_2', text: 'Sometimes', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 's1_3', text: 'Often', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 's1_4', text: 'Always', score: 3, severity: 'severe'),
        ],
        weight: 2,
      ),
      AssessmentQuestion(
        id: 'stress_2',
        category: 'stress',
        question:
            'How often do you feel like you can\'t cope with daily pressures?',
        options: [
          AssessmentOption(
              id: 's2_1', text: 'Never', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 's2_2', text: 'Sometimes', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 's2_3', text: 'Often', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 's2_4', text: 'Always', score: 3, severity: 'severe'),
        ],
        weight: 2,
      ),
      AssessmentQuestion(
        id: 'stress_3',
        category: 'stress',
        question:
            'Do you experience physical symptoms of stress (headaches, muscle tension, stomach issues)?',
        options: [
          AssessmentOption(
              id: 's3_1', text: 'Never', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 's3_2', text: 'Sometimes', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 's3_3', text: 'Often', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 's3_4', text: 'Daily', score: 3, severity: 'severe'),
        ],
        weight: 2,
      ),
    ];
  }

  /// PTSD and Trauma Questions (PCL-5 inspired)
  List<AssessmentQuestion> getPTSDQuestions() {
    return [
      AssessmentQuestion(
        id: 'ptsd_1',
        category: 'ptsd',
        question:
            'Do you have repeated, disturbing memories, thoughts, or images of a stressful experience?',
        options: [
          AssessmentOption(
              id: 'p1_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'p1_2', text: 'A little bit', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'p1_3', text: 'Moderately', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'p1_4', text: 'Quite a bit', score: 3, severity: 'severe'),
          AssessmentOption(
              id: 'p1_5', text: 'Extremely', score: 4, severity: 'critical'),
        ],
        weight: 3,
      ),
      AssessmentQuestion(
        id: 'ptsd_2',
        category: 'ptsd',
        question:
            'Do you avoid activities, places, people, or situations that remind you of a traumatic experience?',
        options: [
          AssessmentOption(
              id: 'p2_1', text: 'Not at all', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'p2_2', text: 'A little bit', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'p2_3', text: 'Moderately', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'p2_4', text: 'Quite a bit', score: 3, severity: 'severe'),
          AssessmentOption(
              id: 'p2_5', text: 'Extremely', score: 4, severity: 'critical'),
        ],
        weight: 2,
      ),
    ];
  }

  /// Bipolar Disorder Questions (MDQ inspired)
  List<AssessmentQuestion> getBipolarQuestions() {
    return [
      AssessmentQuestion(
        id: 'bipolar_1',
        category: 'bipolar',
        question:
            'Have you ever had periods where you felt so good or hyper that others thought you were not your normal self?',
        options: [
          AssessmentOption(
              id: 'b1_1', text: 'No', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'b1_2', text: 'Yes', score: 2, severity: 'moderate'),
        ],
        weight: 3,
      ),
      AssessmentQuestion(
        id: 'bipolar_2',
        category: 'bipolar',
        question:
            'Do you experience extreme mood swings from very high to very low?',
        options: [
          AssessmentOption(
              id: 'b2_1', text: 'Never', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'b2_2', text: 'Sometimes', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'b2_3', text: 'Often', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'b2_4', text: 'Very often', score: 3, severity: 'severe'),
        ],
        weight: 3,
      ),
    ];
  }

  /// OCD Questions (Y-BOCS inspired)
  List<AssessmentQuestion> getOCDQuestions() {
    return [
      AssessmentQuestion(
        id: 'ocd_1',
        category: 'ocd',
        question:
            'Do you have unwanted thoughts, images, or urges that repeatedly enter your mind?',
        options: [
          AssessmentOption(
              id: 'o1_1', text: 'Never', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'o1_2', text: 'Sometimes', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'o1_3', text: 'Often', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'o1_4', text: 'Very often', score: 3, severity: 'severe'),
        ],
        weight: 2,
      ),
      AssessmentQuestion(
        id: 'ocd_2',
        category: 'ocd',
        question:
            'Do you feel compelled to repeat certain behaviors or mental acts?',
        options: [
          AssessmentOption(
              id: 'o2_1', text: 'Never', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'o2_2', text: 'Sometimes', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'o2_3', text: 'Often', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'o2_4', text: 'Very often', score: 3, severity: 'severe'),
        ],
        weight: 2,
      ),
    ];
  }

  /// General Wellbeing Questions
  List<AssessmentQuestion> getGeneralWellbeingQuestions() {
    return [
      AssessmentQuestion(
        id: 'wellbeing_1',
        category: 'wellbeing',
        question: 'How would you rate your overall mental health?',
        options: [
          AssessmentOption(
              id: 'w1_1', text: 'Excellent', score: 0, severity: 'minimal'),
          AssessmentOption(
              id: 'w1_2', text: 'Good', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'w1_3', text: 'Fair', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'w1_4', text: 'Poor', score: 3, severity: 'severe'),
        ],
        weight: 2,
      ),
      AssessmentQuestion(
        id: 'wellbeing_2',
        category: 'wellbeing',
        question: 'How satisfied are you with your current quality of life?',
        options: [
          AssessmentOption(
              id: 'w2_1',
              text: 'Very satisfied',
              score: 0,
              severity: 'minimal'),
          AssessmentOption(
              id: 'w2_2', text: 'Satisfied', score: 1, severity: 'mild'),
          AssessmentOption(
              id: 'w2_3', text: 'Neutral', score: 2, severity: 'moderate'),
          AssessmentOption(
              id: 'w2_4', text: 'Dissatisfied', score: 3, severity: 'severe'),
        ],
        weight: 1,
      ),
    ];
  }

  /// Get questions by category
  List<AssessmentQuestion> getQuestionsByCategory(String category) {
    switch (category.toLowerCase()) {
      case 'anxiety':
        return getAnxietyQuestions();
      case 'depression':
        return getDepressionQuestions();
      case 'stress':
        return getStressQuestions();
      case 'ptsd':
        return getPTSDQuestions();
      case 'bipolar':
        return getBipolarQuestions();
      case 'ocd':
        return getOCDQuestions();
      case 'wellbeing':
        return getGeneralWellbeingQuestions();
      default:
        return [];
    }
  }

  /// Get all available categories
  List<String> getCategories() {
    return [
      'anxiety',
      'depression',
      'stress',
      'ptsd',
      'bipolar',
      'ocd',
      'wellbeing'
    ];
  }

  /// Get category display information
  Map<String, String> getCategoryInfo(String category) {
    final categoryEnum = AssessmentCategory.values.firstWhere(
      (e) => e.name == category,
      orElse: () => AssessmentCategory.generalWellbeing,
    );

    return {
      'name': categoryEnum.displayName,
      'description': categoryEnum.description,
    };
  }
}
