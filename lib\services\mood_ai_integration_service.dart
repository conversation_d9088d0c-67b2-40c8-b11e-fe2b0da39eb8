import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'enhanced_ai_analysis_service.dart';

class MoodAIIntegrationService {
  static final MoodAIIntegrationService _instance = MoodAIIntegrationService._internal();
  factory MoodAIIntegrationService() => _instance;
  MoodAIIntegrationService._internal();

  final EnhancedAIAnalysisService _aiService = EnhancedAIAnalysisService();
  static const String baseUrl = 'http://192.168.1.9:3000/api';

  /// Initialize the service
  Future<void> initialize() async {
    await _aiService.initialize();
    debugPrint('🔗 Mood AI Integration Service initialized');
  }

  /// Process mood entry with AI analysis
  Future<Map<String, dynamic>?> processMoodWithAI({
    required String userId,
    required String moodText,
    required int moodRating,
    required String moodType,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      debugPrint('🎭 Processing mood entry with AI analysis...');
      
      // Perform local AI analysis first
      final aiAnalysis = await _aiService.performEnhancedAnalysis(moodText);
      
      // Prepare mood data with AI insights
      final moodData = {
        'userId': userId,
        'moodText': moodText,
        'moodRating': moodRating,
        'moodType': moodType,
        'aiAnalysis': aiAnalysis,
        'timestamp': DateTime.now().toIso8601String(),
        'additionalData': additionalData ?? {},
      };

      // Send to backend for storage and further processing
      final response = await http.post(
        Uri.parse('$baseUrl/mood-tracking/create-with-ai'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(moodData),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final result = jsonDecode(response.body);
        debugPrint('✅ Mood entry processed with AI analysis');
        return result;
      } else {
        debugPrint('❌ Failed to process mood entry: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error processing mood with AI: $e');
      return null;
    }
  }

  /// Get mood history with AI insights
  Future<List<Map<String, dynamic>>> getMoodHistoryWithAI(String userId, {int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/mood-tracking/history-with-ai/$userId?limit=$limit'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['data'] ?? []);
      } else {
        debugPrint('❌ Failed to get mood history: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('❌ Error getting mood history with AI: $e');
      return [];
    }
  }

  /// Analyze mood patterns using AI
  Future<Map<String, dynamic>?> analyzeMoodPatterns(String userId, {int days = 30}) async {
    try {
      debugPrint('📊 Analyzing mood patterns with AI...');
      
      final response = await http.get(
        Uri.parse('$baseUrl/mood-tracking/patterns-analysis/$userId?days=$days'),
      );

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        debugPrint('✅ Mood patterns analyzed');
        return result;
      } else {
        debugPrint('❌ Failed to analyze mood patterns: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error analyzing mood patterns: $e');
      return null;
    }
  }

  /// Get AI-generated mood insights
  Future<Map<String, dynamic>?> getMoodInsights(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/mood-tracking/ai-insights/$userId'),
      );

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        return result;
      } else {
        debugPrint('❌ Failed to get mood insights: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error getting mood insights: $e');
      return null;
    }
  }

  /// Sync mood data with AI analysis backend
  Future<bool> syncMoodData(String userId) async {
    try {
      debugPrint('🔄 Syncing mood data with AI analysis...');
      
      final response = await http.post(
        Uri.parse('$baseUrl/mood-tracking/sync-ai-analysis/$userId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        debugPrint('✅ Mood data synced successfully');
        return true;
      } else {
        debugPrint('❌ Failed to sync mood data: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error syncing mood data: $e');
      return false;
    }
  }

  /// Generate mood-based recommendations
  Future<List<String>> generateMoodRecommendations(String userId, String currentMood) async {
    try {
      // Perform local AI analysis for immediate recommendations
      final aiAnalysis = await _aiService.performEnhancedAnalysis(currentMood);
      final localRecommendations = aiAnalysis['recommendations'] as List<String>? ?? [];

      // Get additional recommendations from backend
      final response = await http.post(
        Uri.parse('$baseUrl/mood-tracking/recommendations'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'userId': userId,
          'currentMood': currentMood,
          'aiAnalysis': aiAnalysis,
        }),
      );

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        final backendRecommendations = List<String>.from(result['recommendations'] ?? []);
        
        // Combine and deduplicate recommendations
        final allRecommendations = <String>{};
        allRecommendations.addAll(localRecommendations);
        allRecommendations.addAll(backendRecommendations);
        
        return allRecommendations.toList();
      } else {
        // Return local recommendations if backend fails
        return localRecommendations;
      }
    } catch (e) {
      debugPrint('❌ Error generating mood recommendations: $e');
      return [];
    }
  }

  /// Check for mood-based alerts
  Future<List<Map<String, dynamic>>> checkMoodAlerts(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/mood-tracking/alerts/$userId'),
      );

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(result['alerts'] ?? []);
      } else {
        return [];
      }
    } catch (e) {
      debugPrint('❌ Error checking mood alerts: $e');
      return [];
    }
  }

  /// Create mood journal entry with AI analysis
  Future<Map<String, dynamic>?> createMoodJournalEntry({
    required String userId,
    required String content,
    required String mood,
    required int rating,
  }) async {
    try {
      // Perform AI analysis on the journal content
      final aiAnalysis = await _aiService.performEnhancedAnalysis(content);
      
      // Create journal entry with AI insights
      final response = await http.post(
        Uri.parse('$baseUrl/ai-analysis/analyze/$userId'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'content': content,
          'mood': mood,
          'rating': rating,
          'enhancedAnalysis': true,
        }),
      );

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        
        // Also create mood tracking entry
        await processMoodWithAI(
          userId: userId,
          moodText: content,
          moodRating: rating,
          moodType: mood,
          additionalData: {
            'journalEntry': true,
            'aiAnalysis': aiAnalysis,
          },
        );
        
        return result;
      } else {
        debugPrint('❌ Failed to create mood journal entry: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error creating mood journal entry: $e');
      return null;
    }
  }

  /// Get comprehensive mood and AI analysis dashboard data
  Future<Map<String, dynamic>?> getMoodDashboardData(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/mood-tracking/dashboard/$userId'),
      );

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        return result;
      } else {
        debugPrint('❌ Failed to get mood dashboard data: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error getting mood dashboard data: $e');
      return null;
    }
  }

  /// Validate and test the integration
  Future<bool> validateIntegration(String userId) async {
    try {
      debugPrint('🧪 Validating mood AI integration...');
      
      // Test AI analysis
      final testAnalysis = await _aiService.performEnhancedAnalysis(
        'I feel anxious about work and stressed about deadlines'
      );
      
      if (testAnalysis.isEmpty) {
        debugPrint('❌ AI analysis validation failed');
        return false;
      }
      
      // Test backend connectivity
      final response = await http.get(
        Uri.parse('$baseUrl/mood-tracking/health-check'),
      );
      
      if (response.statusCode != 200) {
        debugPrint('❌ Backend connectivity validation failed');
        return false;
      }
      
      debugPrint('✅ Mood AI integration validation successful');
      return true;
    } catch (e) {
      debugPrint('❌ Integration validation error: $e');
      return false;
    }
  }
}
