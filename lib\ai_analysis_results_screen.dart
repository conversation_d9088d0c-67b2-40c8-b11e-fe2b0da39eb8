import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:myapp/theme/app_theme.dart';

class AIAnalysisResultsScreen extends StatefulWidget {
  final String userId;

  const AIAnalysisResultsScreen({
    super.key,
    required this.userId,
  });

  @override
  State<AIAnalysisResultsScreen> createState() =>
      _AIAnalysisResultsScreenState();
}

class _AIAnalysisResultsScreenState extends State<AIAnalysisResultsScreen> {
  bool _isLoading = true;
  Map<String, dynamic>? _analysisData;
  List<Map<String, dynamic>> _moodHistory = [];
  double _moodScore = 7.2;
  String _moodTrend = '+1.5%';
  List<String> _identifiedThemes = [
    'Stress',
    'Anxiety',
    'Relationships',
    'Work',
    'Self-Care'
  ];
  List<Map<String, dynamic>> _insights = [
    {
      'type': 'positive',
      'title': 'Positive Trend',
      'description':
          'Your mood has been consistently positive over the past week.',
      'icon': Icons.sentiment_satisfied,
    },
    {
      'type': 'work',
      'title': 'Work Stress',
      'description':
          'You frequently mention work-related stress in your journal entries.',
      'icon': Icons.work,
    },
    {
      'type': 'relationship',
      'title': 'Relationship Satisfaction',
      'description':
          'You express positive feelings when discussing relationships.',
      'icon': Icons.favorite,
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadAnalysisData();
  }

  Future<void> _loadAnalysisData() async {
    setState(() => _isLoading = true);

    try {
      // Load mood history for chart
      await _loadMoodHistory();

      // Load AI analysis data
      await _loadAIAnalysis();
    } catch (e) {
      print('Error loading analysis data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMoodHistory() async {
    try {
      final response = await http.get(
        Uri.parse('http://***********:3000/api/mood/${widget.userId}'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _moodHistory =
              List<Map<String, dynamic>>.from(data['moodEntries'] ?? []);
        });
      }
    } catch (e) {
      print('Error loading mood history: $e');
    }
  }

  Future<void> _loadAIAnalysis() async {
    try {
      final response = await http.get(
        Uri.parse('http://***********:3000/api/ai-analysis/${widget.userId}'),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _analysisData = data;
        });
      }
    } catch (e) {
      print('Error loading AI analysis: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'AI Analysis Results',
          style: AppTheme.headingMedium.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildMoodTrendSection(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildIdentifiedThemesSection(),
                  const SizedBox(height: AppTheme.spacingL),
                  _buildInsightsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildMoodTrendSection() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Overall Mood Trend',
            style: AppTheme.headingSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),

          // Mood Score
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Mood Score',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _moodScore.toString(),
                    style: AppTheme.headingLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        'Last 7 Days ',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                      Text(
                        _moodTrend,
                        style: AppTheme.bodySmall.copyWith(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingL),

          // Mood Chart
          SizedBox(
            height: 200,
            child: _buildMoodChart(),
          ),

          const SizedBox(height: AppTheme.spacingM),

          // Days of week labels
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                .map((day) => Text(
                      day,
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMoodChart() {
    // Sample data for the mood chart (matching the mockup curve)
    final spots = [
      const FlSpot(0, 6.5),
      const FlSpot(1, 5.0),
      const FlSpot(2, 6.0),
      const FlSpot(3, 5.5),
      const FlSpot(4, 4.0),
      const FlSpot(5, 8.0),
      const FlSpot(6, 7.5),
    ];

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: false),
        titlesData: const FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: AppTheme.primaryColor,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: AppTheme.primaryColor.withOpacity(0.1),
            ),
          ),
        ],
        minX: 0,
        maxX: 6,
        minY: 0,
        maxY: 10,
      ),
    );
  }

  Widget _buildIdentifiedThemesSection() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Identified Themes',
            style: AppTheme.headingSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          Wrap(
            spacing: AppTheme.spacingS,
            runSpacing: AppTheme.spacingS,
            children: _identifiedThemes
                .map((theme) => _buildThemeChip(theme))
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeChip(String theme) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingM,
        vertical: AppTheme.spacingS,
      ),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Text(
        theme,
        style: AppTheme.bodyMedium.copyWith(
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildInsightsSection() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Insights & Patterns',
            style: AppTheme.headingSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          ..._insights.map((insight) => _buildInsightCard(insight)).toList(),
        ],
      ),
    );
  }

  Widget _buildInsightCard(Map<String, dynamic> insight) {
    Color iconColor;
    Color backgroundColor;

    switch (insight['type']) {
      case 'positive':
        iconColor = Colors.green;
        backgroundColor = Colors.green.withOpacity(0.1);
        break;
      case 'work':
        iconColor = Colors.orange;
        backgroundColor = Colors.orange.withOpacity(0.1);
        break;
      case 'relationship':
        iconColor = Colors.pink;
        backgroundColor = Colors.pink.withOpacity(0.1);
        break;
      default:
        iconColor = AppTheme.primaryColor;
        backgroundColor = AppTheme.primaryColor.withOpacity(0.1);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusS),
            ),
            child: Icon(
              insight['icon'],
              color: iconColor,
              size: 20,
            ),
          ),
          const SizedBox(width: AppTheme.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  insight['title'],
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  insight['description'],
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
