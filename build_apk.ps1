#!/usr/bin/env powershell

Write-Host "🚀 Building MindEase Production APK..." -ForegroundColor Green

# Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
flutter clean

# Get dependencies
Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
flutter pub get

# Build APK
Write-Host "🔨 Building release APK..." -ForegroundColor Yellow
flutter build apk --release --verbose

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ APK built successfully!" -ForegroundColor Green
    Write-Host "📱 APK location: build/app/outputs/flutter-apk/app-release.apk" -ForegroundColor Cyan
    
    # Copy APK to root with version name
    $timestamp = Get-Date -Format "yyyy-MM-dd-HHmm"
    $apkName = "mindease-v1.0.0-$timestamp-release.apk"
    Copy-Item "build/app/outputs/flutter-apk/app-release.apk" $apkName
    Write-Host "📋 APK copied to: $apkName" -ForegroundColor Cyan
} else {
    Write-Host "❌ APK build failed!" -ForegroundColor Red
    exit 1
}
