import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import 'package:audioplayers/audioplayers.dart' as ap;
import 'services/enhanced_audio_player_service.dart';
import 'services/audio_enhancement_service.dart';
import 'services/audio_processing_service.dart';

class SoundPlayerScreen extends StatefulWidget {
  final String soundName;
  final String userId;

  const SoundPlayerScreen({
    super.key,
    required this.soundName,
    required this.userId,
  });

  @override
  State<SoundPlayerScreen> createState() => _SoundPlayerScreenState();
}

class _SoundPlayerScreenState extends State<SoundPlayerScreen>
    with TickerProviderStateMixin {
  final EnhancedAudioPlayerService _audioService = EnhancedAudioPlayerService();
  final AudioEnhancementService _enhancementService = AudioEnhancementService();
  bool _isPlaying = false;
  bool _isPaused = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _volume = 1.0;
  bool _isEnhancementEnabled = true;
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<ap.PlayerState>? _stateSubscription;

  // Get sound configuration dynamically
  Map<String, dynamic> get _soundConfig {
    final allConfigs = {
      'Rain Sounds': {
        'color': Color(0xFF607D8B),
        'icon': Icons.grain,
        'description': 'Gentle rain drops for deep relaxation',
        'duration': Duration(minutes: 30),
      },
      'Ocean Waves': {
        'color': Color(0xFF2196F3),
        'icon': Icons.waves,
        'description': 'Calming ocean waves washing ashore',
        'duration': Duration(minutes: 45),
      },
      'Forest Sounds': {
        'color': Color(0xFF4CAF50),
        'icon': Icons.park,
        'description': 'Birds chirping in a peaceful forest',
        'duration': Duration(minutes: 35),
      },
      'White Noise': {
        'color': Color(0xFF9E9E9E),
        'icon': Icons.volume_up,
        'description': 'Consistent background noise for focus',
        'duration': Duration(minutes: 60),
      },
      'Fireplace': {
        'color': Color(0xFFFF5722),
        'icon': Icons.local_fire_department,
        'description': 'Crackling fire sounds for warmth',
        'duration': Duration(minutes: 40),
      },
      'Piano Music': {
        'color': Color(0xFF795548),
        'icon': Icons.piano,
        'description': 'Soft piano melodies for tranquility',
        'duration': Duration(minutes: 25),
      },
      'Nature Birds': {
        'color': Color(0xFF8BC34A),
        'icon': Icons.flutter_dash,
        'description': 'Beautiful bird songs from nature',
        'duration': Duration(minutes: 30),
      },
      'Gentle Rain': {
        'color': Color(0xFF00BCD4),
        'icon': Icons.water_drop,
        'description': 'Soft rain sounds for peaceful sleep',
        'duration': Duration(minutes: 35),
      },
      'Campfire': {
        'color': Color(0xFFFF6F00),
        'icon': Icons.fireplace,
        'description': 'Cozy campfire crackling sounds',
        'duration': Duration(minutes: 40),
      },
      'Waterfall': {
        'color': Color(0xFF03DAC6),
        'icon': Icons.water,
        'description': 'Flowing waterfall for meditation',
        'duration': Duration(minutes: 50),
      },
      'Spring Forest': {
        'color': Color(0xFF689F38),
        'icon': Icons.eco,
        'description': 'Fresh spring forest ambiance',
        'duration': Duration(minutes: 35),
      },
      'Singing Bowl': {
        'color': Color(0xFFFFB74D),
        'icon': Icons.music_note,
        'description': 'Tibetan singing bowl meditation',
        'duration': Duration(minutes: 20),
      },
      'Rain and Wind Chimes': {
        'color': Color(0xFF81C784),
        'icon': Icons.air,
        'description': 'Rain with gentle wind chimes',
        'duration': Duration(minutes: 40),
      },
      'Gentle Breeze': {
        'color': Color(0xFF90CAF9),
        'icon': Icons.air,
        'description': 'Soft breeze through trees',
        'duration': Duration(minutes: 45),
      },
      'Calm Nature': {
        'color': Color(0xFF66BB6A),
        'icon': Icons.nature,
        'description': 'Peaceful nature soundscape',
        'duration': Duration(minutes: 40),
      },
      'Light Rain': {
        'color': Color(0xFF78909C),
        'icon': Icons.cloud,
        'description': 'Light rain for concentration',
        'duration': Duration(minutes: 30),
      },
      'Rain on Window': {
        'color': Color(0xFF546E7A),
        'icon': Icons.window,
        'description': 'Rain gently hitting the window',
        'duration': Duration(minutes: 35),
      },
      'Sleep Music': {
        'color': Color(0xFF7986CB),
        'icon': Icons.bedtime,
        'description': 'Relaxing music for better sleep',
        'duration': Duration(minutes: 60),
      },
    };

    return allConfigs[widget.soundName] ??
        {
          'color': Color(0xFF9C27B0),
          'icon': Icons.music_note,
          'description': 'Relaxing meditation sound',
          'duration': Duration(minutes: 30),
        };
  }

  @override
  void initState() {
    super.initState();

    _waveController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeInOut,
    ));

    _initializeAudioService();
  }

  Future<void> _initializeAudioService() async {
    await _audioService.initialize();

    // Set up stream subscriptions
    _positionSubscription = _audioService.positionStream.listen((position) {
      setState(() {
        _position = position;
      });
    });

    _durationSubscription = _audioService.durationStream.listen((duration) {
      setState(() {
        _duration = duration;
      });
    });

    _stateSubscription = _audioService.stateStream.listen((state) {
      setState(() {
        _isPlaying = state == ap.PlayerState.playing;
        _isPaused = state == ap.PlayerState.paused;

        if (_isPlaying && !_isPaused) {
          _waveController.repeat(reverse: true);
        } else {
          _waveController.stop();
        }
      });
    });

    // Auto-start playback
    _startPlayback();
  }

  Future<void> _startPlayback() async {
    try {
      EnhancementSettings? settings;

      if (_isEnhancementEnabled) {
        settings = _enhancementService.getOptimalSettings(widget.soundName);
      }

      final success = await _audioService.playSound(
        widget.soundName,
        processingOptions: settings != null
            ? AudioProcessingOptions(
                volumeGain: settings.targetVolume,
                noiseReduction: settings.noiseReduction,
                normalize: settings.volumeNormalization,
                fadeInDuration: settings.fadeIn,
                fadeOutDuration: settings.fadeOut,
              )
            : null,
      );

      if (!success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to play ${widget.soundName}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error starting playback: $e');
    }
  }

  Future<void> _pausePlayback() async {
    if (_isPlaying && !_isPaused) {
      await _audioService.pause();
    } else if (_isPaused) {
      await _audioService.resume();
    }
  }

  Future<void> _stopPlayback() async {
    await _audioService.stop();
  }

  Future<void> _setVolume(double volume) async {
    setState(() {
      _volume = volume;
    });
    await _audioService.setVolume(volume);
  }

  void _showVolumeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Volume Control'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Volume: ${(_volume * 100).round()}%'),
            const SizedBox(height: 16),
            Slider(
              value: _volume,
              min: 0.0,
              max: 1.0,
              divisions: 20,
              onChanged: (value) {
                _setVolume(value);
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('Enhancement: '),
                Switch(
                  value: _isEnhancementEnabled,
                  onChanged: (value) {
                    setState(() {
                      _isEnhancementEnabled = value;
                    });
                  },
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    final config = _soundConfig;
    final soundColor = config['color'] as Color;
    final soundIcon = config['icon'] as IconData;
    final description = config['description'] as String;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          widget.soundName,
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.0,
            colors: [
              soundColor.withOpacity(0.3),
              Colors.black,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Sound visualization
              AnimatedBuilder(
                animation: _waveAnimation,
                builder: (context, child) {
                  return Container(
                    width: 200 + (_waveAnimation.value * 50),
                    height: 200 + (_waveAnimation.value * 50),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: soundColor
                            .withOpacity(0.5 + _waveAnimation.value * 0.5),
                        width: 3,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: soundColor.withOpacity(0.3),
                          blurRadius: 30 + (_waveAnimation.value * 20),
                          spreadRadius: 10 + (_waveAnimation.value * 10),
                        ),
                      ],
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: soundColor.withOpacity(0.1),
                      ),
                      child: Center(
                        child: Icon(
                          soundIcon,
                          color: soundColor,
                          size: 80,
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 40),

              // Sound name and description
              Text(
                widget.soundName,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: soundColor,
                ),
              ),

              const SizedBox(height: 8),

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Text(
                  description,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.7),
                  ),
                ),
              ),

              const SizedBox(height: 40),

              // Progress bar
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Column(
                  children: [
                    LinearProgressIndicator(
                      value: _duration.inSeconds > 0
                          ? _position.inSeconds / _duration.inSeconds
                          : 0,
                      backgroundColor: Colors.white.withOpacity(0.2),
                      valueColor: AlwaysStoppedAnimation<Color>(soundColor),
                      minHeight: 4,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatDuration(_position),
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          _formatDuration(_duration),
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 40),

              // Control buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Stop button
                  GestureDetector(
                    onTap: _stopPlayback,
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.red.withOpacity(0.8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withOpacity(0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.stop,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                  ),

                  // Play/Pause button
                  GestureDetector(
                    onTap: _isPlaying ? _pausePlayback : _startPlayback,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: soundColor,
                        boxShadow: [
                          BoxShadow(
                            color: soundColor.withOpacity(0.5),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Icon(
                        _isPaused ? Icons.play_arrow : Icons.pause,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),

                  // Volume button
                  GestureDetector(
                    onTap: () {
                      _showVolumeDialog();
                    },
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey.withOpacity(0.8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.volume_up,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 40),

              // Status text
              Text(
                _isPlaying ? (_isPaused ? 'Paused' : 'Playing...') : 'Stopped',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _stateSubscription?.cancel();
    _waveController.dispose();
    _audioService.dispose();
    super.dispose();
  }
}
