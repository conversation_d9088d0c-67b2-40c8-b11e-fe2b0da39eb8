import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/homepage/therapist_detail_screen.dart';

class TherapistsScreen extends StatefulWidget {
  final String userId;

  const TherapistsScreen({
    super.key,
    required this.userId,
  });

  @override
  State<TherapistsScreen> createState() => _TherapistsScreenState();
}

class _TherapistsScreenState extends State<TherapistsScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  List<Map<String, dynamic>> _allTherapists = [];
  List<Map<String, dynamic>> _filteredTherapists = [];
  List<Map<String, dynamic>> _myTherapists = [];

  // Removed sample data - now using real API data only

  @override
  void initState() {
    super.initState();
    _loadTherapists();
    _searchController.addListener(_filterTherapists);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadTherapists() async {
    setState(() => _isLoading = true);

    try {
      // Load all therapists
      final response = await http.get(
        Uri.parse('http://192.168.1.9:3000/api/therapists'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> therapists = jsonDecode(response.body);
        setState(() {
          _allTherapists = therapists.cast<Map<String, dynamic>>();
          _filteredTherapists = _allTherapists;
        });
      } else {
        // No fallback data - show empty state
        setState(() {
          _allTherapists = [];
          _filteredTherapists = [];
        });
      }

      // Load user's therapists (for "My Therapists" section)
      await _loadMyTherapists();
    } catch (e) {
      debugPrint('Error loading therapists: $e');
      // Show empty state on error
      setState(() {
        _allTherapists = [];
        _filteredTherapists = [];
        _myTherapists = [];
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMyTherapists() async {
    try {
      // Fetch user's booked therapists from API
      final response = await http.get(
        Uri.parse(
            'http://192.168.1.9:3000/api/users/${widget.userId}/therapists'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _myTherapists =
              List<Map<String, dynamic>>.from(data['therapists'] ?? []);
        });
      } else {
        setState(() {
          _myTherapists = [];
        });
      }
    } catch (e) {
      debugPrint('Error loading my therapists: $e');
      setState(() {
        _myTherapists = [];
      });
    }
  }

  void _filterTherapists() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredTherapists = _allTherapists.where((therapist) {
        final name = (therapist['name'] ?? '').toLowerCase();
        final specialty =
            (therapist['specialty'] ?? therapist['specialization'] ?? '')
                .toLowerCase();
        return name.contains(query) || specialty.contains(query);
      }).toList();
    });
  }

  void _scheduleAppointment(Map<String, dynamic> therapist) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TherapistDetailScreen(
          therapist: therapist,
          userId: widget.userId,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Therapists',
          style: AppTheme.headingMedium.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.cardColor,
                borderRadius: BorderRadius.circular(AppTheme.radiusL),
                boxShadow: AppTheme.softShadow,
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search for a therapist',
                  hintStyle: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: AppTheme.textSecondary,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacingM,
                    vertical: AppTheme.spacingM,
                  ),
                ),
              ),
            ),
          ),

          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacingM),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // My Therapists Section
                        if (_myTherapists.isNotEmpty) ...[
                          Text(
                            'My Therapists',
                            style: AppTheme.headingSmall.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppTheme.spacingM),
                          ..._myTherapists.map((therapist) =>
                              _buildTherapistCard(therapist,
                                  isMyTherapist: true)),
                          const SizedBox(height: AppTheme.spacingL),
                        ],

                        // All Therapists Section
                        Text(
                          _searchController.text.isEmpty
                              ? 'All Therapists'
                              : 'Search Results',
                          style: AppTheme.headingSmall.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppTheme.spacingM),

                        if (_filteredTherapists.isEmpty)
                          _buildEmptyState()
                        else
                          ..._filteredTherapists.map(
                              (therapist) => _buildTherapistCard(therapist)),
                      ],
                    ),
                  ),
          ),

          // Schedule Appointment Button
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  // Show appointment scheduling options
                  _showScheduleOptions();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  padding:
                      const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusL),
                  ),
                ),
                child: Text(
                  'Schedule Appointment',
                  style: AppTheme.bodyLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppTheme.textSecondary,
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              'No therapists found',
              style: AppTheme.headingSmall.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              'Try adjusting your search terms',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTherapistCard(Map<String, dynamic> therapist,
      {bool isMyTherapist = false}) {
    final name = therapist['name'] ?? 'Unknown Therapist';
    final specialty = therapist['specialty'] ??
        therapist['specialization'] ??
        'General Therapy';
    final profileImage = therapist['profileImage'];

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: AppTheme.softShadow,
      ),
      child: Row(
        children: [
          // Profile Avatar
          CircleAvatar(
            radius: 28,
            backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
            backgroundImage: profileImage != null
                ? MemoryImage(base64Decode(profileImage.split(',')[1]))
                : null,
            child: profileImage == null
                ? Text(
                    name.isNotEmpty ? name[0].toUpperCase() : 'T',
                    style: AppTheme.bodyLarge.copyWith(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),

          const SizedBox(width: AppTheme.spacingM),

          // Therapist Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: AppTheme.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  specialty,
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Action Button
          if (isMyTherapist)
            TextButton(
              onPressed: () => _scheduleAppointment(therapist),
              child: Text(
                'Book Again',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            )
          else
            ElevatedButton(
              onPressed: () => _scheduleAppointment(therapist),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                foregroundColor: AppTheme.primaryColor,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingM,
                  vertical: AppTheme.spacingS,
                ),
              ),
              child: const Text(
                'Book',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
        ],
      ),
    );
  }

  void _showScheduleOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Schedule Appointment',
              style: AppTheme.headingSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingL),
            ListTile(
              leading: const Icon(Icons.video_call),
              title: const Text('Online Consultation'),
              subtitle: const Text('Video call with therapist'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to online booking
              },
            ),
            ListTile(
              leading: const Icon(Icons.location_on),
              title: const Text('In-Person Session'),
              subtitle: const Text('Meet at therapist\'s office'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to physical booking
              },
            ),
          ],
        ),
      ),
    );
  }
}
