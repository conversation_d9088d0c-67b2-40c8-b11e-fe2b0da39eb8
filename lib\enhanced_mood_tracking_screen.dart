import 'package:flutter/material.dart';
import 'package:myapp/theme/app_theme.dart';
import 'services/mood_ai_integration_service.dart';
import 'dart:async';

class EnhancedMoodTrackingScreen extends StatefulWidget {
  final String userId;

  const EnhancedMoodTrackingScreen({super.key, required this.userId});

  @override
  State<EnhancedMoodTrackingScreen> createState() => _EnhancedMoodTrackingScreenState();
}

class _EnhancedMoodTrackingScreenState extends State<EnhancedMoodTrackingScreen>
    with SingleTickerProviderStateMixin {
  final MoodAIIntegrationService _moodAIService = MoodAIIntegrationService();
  final TextEditingController _moodTextController = TextEditingController();
  
  late TabController _tabController;
  
  String _selectedMood = '';
  int _moodRating = 5;
  bool _isLoading = false;
  bool _isAnalyzing = false;
  List<Map<String, dynamic>> _moodHistory = [];
  Map<String, dynamic>? _currentAIAnalysis;
  List<String> _aiRecommendations = [];
  Map<String, dynamic>? _moodInsights;

  final List<Map<String, dynamic>> _moodOptions = [
    {'name': 'Happy', 'emoji': '😊', 'color': Colors.green},
    {'name': 'Sad', 'emoji': '😢', 'color': Colors.blue},
    {'name': 'Anxious', 'emoji': '😰', 'color': Colors.orange},
    {'name': 'Angry', 'emoji': '😠', 'color': Colors.red},
    {'name': 'Excited', 'emoji': '🤩', 'color': Colors.purple},
    {'name': 'Calm', 'emoji': '😌', 'color': Colors.teal},
    {'name': 'Stressed', 'emoji': '😫', 'color': Colors.deepOrange},
    {'name': 'Content', 'emoji': '😊', 'color': Colors.lightGreen},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeService();
    _loadMoodHistory();
    _loadMoodInsights();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _moodTextController.dispose();
    super.dispose();
  }

  Future<void> _initializeService() async {
    await _moodAIService.initialize();
  }

  Future<void> _loadMoodHistory() async {
    setState(() => _isLoading = true);
    try {
      final history = await _moodAIService.getMoodHistoryWithAI(widget.userId);
      setState(() => _moodHistory = history);
    } catch (e) {
      debugPrint('Error loading mood history: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMoodInsights() async {
    try {
      final insights = await _moodAIService.getMoodInsights(widget.userId);
      setState(() => _moodInsights = insights);
    } catch (e) {
      debugPrint('Error loading mood insights: $e');
    }
  }

  Future<void> _analyzeCurrentMood() async {
    if (_moodTextController.text.trim().isEmpty) return;

    setState(() => _isAnalyzing = true);
    try {
      final recommendations = await _moodAIService.generateMoodRecommendations(
        widget.userId,
        _moodTextController.text.trim(),
      );
      setState(() => _aiRecommendations = recommendations);
    } catch (e) {
      debugPrint('Error analyzing mood: $e');
    } finally {
      setState(() => _isAnalyzing = false);
    }
  }

  Future<void> _saveMoodEntry() async {
    if (_selectedMood.isEmpty || _moodTextController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a mood and add some notes')),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      final result = await _moodAIService.processMoodWithAI(
        userId: widget.userId,
        moodText: _moodTextController.text.trim(),
        moodRating: _moodRating,
        moodType: _selectedMood,
      );

      if (result != null) {
        setState(() {
          _selectedMood = '';
          _moodRating = 5;
          _moodTextController.clear();
          _aiRecommendations.clear();
        });
        
        await _loadMoodHistory();
        await _loadMoodInsights();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Mood entry saved with AI analysis!')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to save mood entry')),
          );
        }
      }
    } catch (e) {
      debugPrint('Error saving mood entry: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppTheme.backgroundGradient),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildLogMoodTab(),
                    _buildHistoryTab(),
                    _buildInsightsTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppTheme.radiusXL),
          bottomRight: Radius.circular(AppTheme.radiusXL),
        ),
        boxShadow: AppTheme.mediumShadow,
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          ),
          const SizedBox(width: AppTheme.spacingS),
          Expanded(
            child: Text(
              'Enhanced Mood Tracking',
              style: AppTheme.headingMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            child: const Icon(Icons.psychology, color: Colors.white, size: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(AppTheme.radiusL),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary,
        tabs: const [
          Tab(icon: Icon(Icons.add_reaction), text: 'Log Mood'),
          Tab(icon: Icon(Icons.history), text: 'History'),
          Tab(icon: Icon(Icons.insights), text: 'AI Insights'),
        ],
      ),
    );
  }

  Widget _buildLogMoodTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMoodSelector(),
          const SizedBox(height: AppTheme.spacingL),
          _buildMoodRatingSlider(),
          const SizedBox(height: AppTheme.spacingL),
          _buildMoodTextInput(),
          const SizedBox(height: AppTheme.spacingL),
          if (_aiRecommendations.isNotEmpty) _buildAIRecommendations(),
          const SizedBox(height: AppTheme.spacingL),
          _buildSaveButton(),
        ],
      ),
    );
  }

  Widget _buildMoodSelector() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'How are you feeling?',
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          Wrap(
            spacing: AppTheme.spacingS,
            runSpacing: AppTheme.spacingS,
            children: _moodOptions.map((mood) => _buildMoodChip(mood)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMoodChip(Map<String, dynamic> mood) {
    final isSelected = _selectedMood == mood['name'];
    return GestureDetector(
      onTap: () => setState(() => _selectedMood = mood['name']),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingM,
          vertical: AppTheme.spacingS,
        ),
        decoration: BoxDecoration(
          color: isSelected ? mood['color'] : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(AppTheme.radiusL),
          border: Border.all(
            color: isSelected ? mood['color'] : Colors.grey.shade300,
            width: 2,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(mood['emoji'], style: const TextStyle(fontSize: 20)),
            const SizedBox(width: AppTheme.spacingS),
            Text(
              mood['name'],
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMoodRatingSlider() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Intensity Level: $_moodRating/10',
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          Slider(
            value: _moodRating.toDouble(),
            min: 1,
            max: 10,
            divisions: 9,
            activeColor: AppTheme.primaryColor,
            onChanged: (value) => setState(() => _moodRating = value.round()),
          ),
        ],
      ),
    );
  }

  Widget _buildMoodTextInput() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.edit_note, color: AppTheme.primaryColor, size: 24),
              const SizedBox(width: AppTheme.spacingS),
              Text(
                'Tell us more about your mood',
                style: AppTheme.headingSmall.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),
          TextField(
            controller: _moodTextController,
            decoration: InputDecoration(
              hintText: 'Describe your feelings, thoughts, or what happened today...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
                borderSide: BorderSide(color: AppTheme.primaryColor.withOpacity(0.3)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
              ),
            ),
            maxLines: 4,
            minLines: 3,
            onChanged: (_) => _analyzeCurrentMood(),
          ),
          if (_isAnalyzing)
            const Padding(
              padding: EdgeInsets.only(top: AppTheme.spacingS),
              child: Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: AppTheme.spacingS),
                  Text('AI is analyzing your mood...'),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAIRecommendations() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: AppTheme.secondaryColor, size: 24),
              const SizedBox(width: AppTheme.spacingS),
              Text(
                'AI Recommendations',
                style: AppTheme.headingSmall.copyWith(
                  color: AppTheme.secondaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),
          ..._aiRecommendations.map((recommendation) => Padding(
            padding: const EdgeInsets.only(bottom: AppTheme.spacingS),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.check_circle, color: AppTheme.secondaryColor, size: 16),
                const SizedBox(width: AppTheme.spacingS),
                Expanded(
                  child: Text(
                    recommendation,
                    style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveMoodEntry,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusM),
          ),
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.save),
                  const SizedBox(width: AppTheme.spacingS),
                  Text(
                    'Save Mood Entry with AI Analysis',
                    style: AppTheme.bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildHistoryTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_moodHistory.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: Colors.grey),
            SizedBox(height: AppTheme.spacingM),
            Text('No mood entries yet', style: TextStyle(fontSize: 18, color: Colors.grey)),
            SizedBox(height: AppTheme.spacingS),
            Text('Start tracking your mood to see insights here', textAlign: TextAlign.center),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      itemCount: _moodHistory.length,
      itemBuilder: (context, index) => _buildMoodHistoryCard(_moodHistory[index]),
    );
  }

  Widget _buildMoodHistoryCard(Map<String, dynamic> entry) {
    final aiAnalysis = entry['aiAnalysis'] as Map<String, dynamic>?;
    final mentalHealthStatus = aiAnalysis?['mentalHealthStatus'] as Map<String, dynamic>?;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                entry['timestamp']?.toString().split('T')[0] ?? 'Unknown date',
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacingS, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusS),
                ),
                child: Text(
                  '${entry['moodType']} (${entry['moodRating']}/10)',
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            entry['moodText']?.toString() ?? '',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
          ),
          if (mentalHealthStatus != null) ...[
            const SizedBox(height: AppTheme.spacingS),
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingS),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusS),
              ),
              child: Text(
                'AI Analysis: ${mentalHealthStatus['primary']?.toString().toUpperCase() ?? 'Unknown'}',
                style: AppTheme.bodySmall.copyWith(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      child: Column(
        children: [
          if (_moodInsights != null) ...[
            _buildInsightCard('Mood Patterns', _moodInsights!['patterns']?.toString() ?? 'No patterns detected'),
            const SizedBox(height: AppTheme.spacingM),
            _buildInsightCard('AI Recommendations', _moodInsights!['recommendations']?.toString() ?? 'No recommendations available'),
          ] else
            const Center(
              child: Text('Loading insights...', style: TextStyle(color: Colors.grey)),
            ),
        ],
      ),
    );
  }

  Widget _buildInsightCard(String title, String content) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            content,
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
          ),
        ],
      ),
    );
  }
}
