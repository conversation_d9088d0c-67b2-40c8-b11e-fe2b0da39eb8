import 'package:audioplayers/audioplayers.dart' as ap;
import 'package:flutter/foundation.dart';
import 'dart:async';
import 'audio_processing_service.dart';
import 'audio_performance_manager.dart';
import 'audio_enhancement_service.dart';

class EnhancedAudioPlayerService {
  static final EnhancedAudioPlayerService _instance =
      EnhancedAudioPlayerService._internal();
  factory EnhancedAudioPlayerService() => _instance;
  EnhancedAudioPlayerService._internal();

  final ap.AudioPlayer _audioPlayer = ap.AudioPlayer();
  final AudioProcessingService _processingService = AudioProcessingService();

  // Stream controllers for real-time updates
  final StreamController<Duration> _positionController =
      StreamController<Duration>.broadcast();
  final StreamController<Duration> _durationController =
      StreamController<Duration>.broadcast();
  final StreamController<ap.PlayerState> _stateController =
      StreamController<ap.PlayerState>.broadcast();

  // Current playback state
  String? _currentSound;
  bool _isPlaying = false;
  bool _isPaused = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _volume = 1.0;

  // Getters for streams
  Stream<Duration> get positionStream => _positionController.stream;
  Stream<Duration> get durationStream => _durationController.stream;
  Stream<ap.PlayerState> get stateStream => _stateController.stream;

  // Getters for current state
  bool get isPlaying => _isPlaying;
  bool get isPaused => _isPaused;
  Duration get duration => _duration;
  Duration get position => _position;
  double get volume => _volume;
  String? get currentSound => _currentSound;

  /// Initialize the audio player service
  Future<void> initialize() async {
    try {
      // Set up audio player listeners
      _audioPlayer.onDurationChanged.listen((duration) {
        _duration = duration;
        _durationController.add(duration);
        debugPrint('🎵 Duration changed: ${_formatDuration(duration)}');
      });

      _audioPlayer.onPositionChanged.listen((position) {
        _position = position;
        _positionController.add(position);
      });

      _audioPlayer.onPlayerStateChanged.listen((state) {
        _isPlaying = state == ap.PlayerState.playing;
        _isPaused = state == ap.PlayerState.paused;
        _stateController.add(state);

        debugPrint('🎵 Player state changed: $state');

        if (state == ap.PlayerState.completed) {
          _onPlaybackCompleted();
        }
      });

      debugPrint('✅ Enhanced Audio Player Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing audio player service: $e');
    }
  }

  /// Play a sound with optional processing
  Future<bool> playSound(String soundName,
      {AudioProcessingOptions? processingOptions}) async {
    try {
      debugPrint('🎵 Starting playback: $soundName');

      // Stop current playback if any
      await stop();

      // Load and process audio file
      final audioData = processingOptions != null
          ? await _processingService.processAudio(soundName, processingOptions)
          : await _processingService.loadAudioFile(soundName);

      if (audioData == null) {
        debugPrint('❌ Failed to load audio data for: $soundName');
        return false;
      }

      // Get file path for audioplayers
      final filePath = AudioProcessingService.soundFileMappings[soundName];
      if (filePath == null) {
        debugPrint('❌ No file path found for: $soundName');
        return false;
      }

      // Play the audio file
      await _audioPlayer.play(ap.AssetSource(filePath));

      _currentSound = soundName;
      _isPlaying = true;
      _isPaused = false;

      debugPrint('✅ Started playing: $soundName');
      return true;
    } catch (e) {
      debugPrint('❌ Error playing sound $soundName: $e');
      return false;
    }
  }

  /// Pause playback
  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
      _isPaused = true;
      _isPlaying = false;
      debugPrint('⏸️ Playback paused');
    } catch (e) {
      debugPrint('❌ Error pausing playback: $e');
    }
  }

  /// Resume playback
  Future<void> resume() async {
    try {
      await _audioPlayer.resume();
      _isPaused = false;
      _isPlaying = true;
      debugPrint('▶️ Playback resumed');
    } catch (e) {
      debugPrint('❌ Error resuming playback: $e');
    }
  }

  /// Stop playback
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _isPlaying = false;
      _isPaused = false;
      _position = Duration.zero;
      _positionController.add(_position);
      debugPrint('⏹️ Playback stopped');
    } catch (e) {
      debugPrint('❌ Error stopping playback: $e');
    }
  }

  /// Set volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    try {
      _volume = volume.clamp(0.0, 1.0);
      await _audioPlayer.setVolume(_volume);
      debugPrint('🔊 Volume set to: ${(_volume * 100).toInt()}%');
    } catch (e) {
      debugPrint('❌ Error setting volume: $e');
    }
  }

  /// Seek to specific position
  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
      debugPrint('⏭️ Seeked to: ${_formatDuration(position)}');
    } catch (e) {
      debugPrint('❌ Error seeking: $e');
    }
  }

  /// Set playback rate (0.5 to 2.0)
  Future<void> setPlaybackRate(double rate) async {
    try {
      final clampedRate = rate.clamp(0.5, 2.0);
      await _audioPlayer.setPlaybackRate(clampedRate);
      debugPrint('⚡ Playback rate set to: ${clampedRate}x');
    } catch (e) {
      debugPrint('❌ Error setting playback rate: $e');
    }
  }

  /// Get audio information for current sound
  Future<AudioFileInfo?> getCurrentAudioInfo() async {
    if (_currentSound == null) return null;
    return await _processingService.getAudioInfo(_currentSound!);
  }

  /// Handle playback completion
  void _onPlaybackCompleted() {
    _isPlaying = false;
    _isPaused = false;
    _position = Duration.zero;
    _positionController.add(_position);
    debugPrint('🏁 Playback completed');
  }

  /// Format duration for display
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  /// Dispose resources
  void dispose() {
    _audioPlayer.dispose();
    _positionController.close();
    _durationController.close();
    _stateController.close();
    debugPrint('🧹 Enhanced Audio Player Service disposed');
  }
}
