import 'package:flutter/material.dart';
import 'package:myapp/theme/app_theme.dart';

// Mock classes for assessment system
class AssessmentQuestion {
  final String id;
  final String text;
  final String question;
  final String? description;
  final String category;
  final double weight;
  final List<AssessmentOption> options;

  AssessmentQuestion(
      {required this.id,
      required this.text,
      required this.question,
      this.description,
      required this.category,
      this.weight = 1.0,
      required this.options});
}

class AssessmentOption {
  final String id;
  final String text;
  final int score;

  AssessmentOption({required this.id, required this.text, required this.score});
}

class AssessmentResponse {
  final String questionId;
  final String optionId;
  final double score;
  final String? category;
  final DateTime timestamp;

  AssessmentResponse({
    required this.questionId,
    required this.optionId,
    required this.score,
    this.category,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

class MentalHealthAssessmentService {
  Future<void> initialize() async {
    // Mock initialization
  }

  Future<Map<String, dynamic>> processAssessment(
      List<dynamic> responses) async {
    // Mock processing
    return {'result': 'processed'};
  }
}

class AssessmentQuestionsService {
  List<AssessmentQuestion> getAllQuestions() {
    return []; // Mock questions
  }

  List<String> getCategories() {
    return ['General', 'Anxiety', 'Depression'];
  }

  Map<String, String> getCategoryInfo(String category) {
    return {'title': category, 'description': 'Category description'};
  }

  List<AssessmentQuestion> getQuestionsByCategory(String category) {
    return []; // Mock questions for category
  }
}

class AssessmentResultsScreen extends StatelessWidget {
  final Map<String, dynamic> result;

  const AssessmentResultsScreen({super.key, required this.result});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Assessment Results')),
      body: const Center(child: Text('Results will be displayed here')),
    );
  }
}

class MentalHealthAssessmentScreen extends StatefulWidget {
  const MentalHealthAssessmentScreen({super.key});

  @override
  State<MentalHealthAssessmentScreen> createState() =>
      _MentalHealthAssessmentScreenState();
}

class _MentalHealthAssessmentScreenState
    extends State<MentalHealthAssessmentScreen> with TickerProviderStateMixin {
  int _currentQuestionIndex = 0;
  final Map<int, String> _answers = {};

  // Required properties for the assessment system
  late TabController _tabController;
  bool _isLoading = false;
  bool _isInitialized = false;
  final _assessmentService = MentalHealthAssessmentService();
  final _questionsService = AssessmentQuestionsService();
  List<dynamic> _questions = [];
  Map<String, dynamic> _responses = {};

  final List<Map<String, dynamic>> _sampleQuestions = [
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by the following problems?',
      'subtext': 'Feeling nervous, anxious, or on edge',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day',
      ],
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by the following problems?',
      'subtext': 'Not being able to stop or control worrying',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day',
      ],
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by the following problems?',
      'subtext': 'Worrying too much about different things',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day',
      ],
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by the following problems?',
      'subtext': 'Trouble relaxing',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day',
      ],
    },
    {
      'question':
          'Over the last 2 weeks, how often have you been bothered by the following problems?',
      'subtext': 'Being so restless that it is hard to sit still',
      'options': [
        'Not at all',
        'Several days',
        'More than half the days',
        'Nearly every day',
      ],
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeAssessment();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeAssessment() async {
    setState(() => _isLoading = true);

    try {
      await _assessmentService.initialize();
      _questions = _questionsService.getAllQuestions();

      // Initialize tab controller with categories
      final categories = _questionsService.getCategories();
      _tabController = TabController(length: categories.length, vsync: this);

      setState(() {
        _isInitialized = true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error initializing assessment: $e')),
        );
      }
    }
  }

  void _answerQuestion(AssessmentQuestion question, AssessmentOption option) {
    setState(() {
      _responses[question.id] = AssessmentResponse(
        questionId: question.id,
        optionId: option.id,
        score: option.score * question.weight,
        category: question.category,
        timestamp: DateTime.now(),
      );
    });
  }

  Future<void> _submitAssessment() async {
    if (_responses.length < _questions.length * 0.7) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
              'Please answer at least 70% of the questions to get accurate results'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final responses = _responses.values.toList();
      final result = await _assessmentService.processAssessment(responses);

      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AssessmentResultsScreen(result: result),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error processing assessment: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        body: Container(
          decoration:
              const BoxDecoration(gradient: AppTheme.backgroundGradient),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(color: Colors.white),
                SizedBox(height: 16),
                Text(
                  'Initializing Mental Health Assessment...',
                  style: TextStyle(color: Colors.white, fontSize: 16),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (!_isInitialized) {
      return Scaffold(
        body: Container(
          decoration:
              const BoxDecoration(gradient: AppTheme.backgroundGradient),
          child: const Center(
            child: Text(
              'Failed to initialize assessment',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
          ),
        ),
      );
    }

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppTheme.backgroundGradient),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              _buildProgressIndicator(),
              _buildDisclaimerCard(),
              Expanded(child: _buildAssessmentContent()),
              _buildBottomActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppTheme.radiusXL),
          bottomRight: Radius.circular(AppTheme.radiusXL),
        ),
        boxShadow: AppTheme.mediumShadow,
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          ),
          const SizedBox(width: AppTheme.spacingS),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Mental Health Assessment',
                  style: AppTheme.headingMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Comprehensive mental health screening',
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            child: const Icon(Icons.psychology, color: Colors.white, size: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final progress = _responses.length / _questions.length;
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress',
                style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                '${_responses.length}/${_questions.length}',
                style:
                    AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingS),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildDisclaimerCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20),
          const SizedBox(width: AppTheme.spacingS),
          Expanded(
            child: Text(
              'This assessment is for informational purposes only and does not replace professional medical advice.',
              style: AppTheme.bodySmall.copyWith(color: Colors.blue.shade700),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssessmentContent() {
    final categories = _questionsService.getCategories();

    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        children: [
          TabBar(
            controller: _tabController,
            isScrollable: true,
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondary,
            indicatorColor: AppTheme.primaryColor,
            tabs: categories.map((category) {
              final info = _questionsService.getCategoryInfo(category);
              return Tab(text: info['name']);
            }).toList(),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: categories
                  .map((category) => _buildCategoryQuestions(category))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryQuestions(String category) {
    final questions = _questionsService.getQuestionsByCategory(category);
    final categoryInfo = _questionsService.getCategoryInfo(category);

    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            categoryInfo['name'] ?? category,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            categoryInfo['description'] ?? '',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textSecondary),
          ),
          const SizedBox(height: AppTheme.spacingL),
          Expanded(
            child: ListView.builder(
              itemCount: questions.length,
              itemBuilder: (context, index) =>
                  _buildQuestionCard(questions[index]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionCard(AssessmentQuestion question) {
    final isAnswered = _responses.containsKey(question.id);
    final selectedResponse = _responses[question.id];

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: isAnswered ? Colors.green.shade50 : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        border: Border.all(
          color: isAnswered ? Colors.green.shade200 : Colors.grey.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (isAnswered)
                Icon(Icons.check_circle,
                    color: Colors.green.shade600, size: 20),
              const SizedBox(width: AppTheme.spacingS),
              Expanded(
                child: Text(
                  question.question,
                  style:
                      AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
          if (question.description != null) ...[
            const SizedBox(height: AppTheme.spacingS),
            Text(
              question.description!,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          const SizedBox(height: AppTheme.spacingM),
          ...question.options.map((option) =>
              _buildOptionButton(question, option, selectedResponse)),
        ],
      ),
    );
  }

  Widget _buildOptionButton(AssessmentQuestion question,
      AssessmentOption option, AssessmentResponse? selectedResponse) {
    final isSelected = selectedResponse?.optionId == option.id;

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
      child: InkWell(
        onTap: () => _answerQuestion(question, option),
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        child: Container(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          decoration: BoxDecoration(
            color: isSelected
                ? AppTheme.primaryColor.withOpacity(0.1)
                : Colors.white,
            borderRadius: BorderRadius.circular(AppTheme.radiusM),
            border: Border.all(
              color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Radio<String>(
                value: option.id,
                groupValue: selectedResponse?.optionId,
                onChanged: (_) => _answerQuestion(question, option),
                activeColor: AppTheme.primaryColor,
              ),
              const SizedBox(width: AppTheme.spacingS),
              Expanded(
                child: Text(
                  option.text,
                  style: AppTheme.bodyMedium.copyWith(
                    color: isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.textPrimary,
                    fontWeight:
                        isSelected ? FontWeight.w500 : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    final completionPercentage = (_responses.length / _questions.length) * 100;
    final canSubmit = completionPercentage >= 70;

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        children: [
          if (!canSubmit)
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingS),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning_amber,
                      color: Colors.orange.shade600, size: 20),
                  const SizedBox(width: AppTheme.spacingS),
                  Expanded(
                    child: Text(
                      'Complete at least 70% of questions for accurate results (${completionPercentage.toInt()}% done)',
                      style: AppTheme.bodySmall
                          .copyWith(color: Colors.orange.shade700),
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: AppTheme.spacingM),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: canSubmit && !_isLoading ? _submitAssessment : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.assessment),
                        const SizedBox(width: AppTheme.spacingS),
                        Text(
                          'Complete Assessment',
                          style: AppTheme.bodyLarge.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
