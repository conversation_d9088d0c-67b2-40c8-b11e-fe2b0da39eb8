import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:myapp/theme/app_theme.dart';

class BlockedUsersScreen extends StatefulWidget {
  final String userId;

  const BlockedUsersScreen({
    super.key,
    required this.userId,
  });

  @override
  State<BlockedUsersScreen> createState() => _BlockedUsersScreenState();
}

class _BlockedUsersScreenState extends State<BlockedUsersScreen> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _blockedUsers = [];

  // Sample data matching the mockup
  final List<Map<String, dynamic>> _sampleBlockedUsers = [
    {
      '_id': '1',
      'username': '<PERSON>',
      'handle': '@jennifer_m',
      'profilePhoto': null,
      'isBlocked': true,
    },
    {
      '_id': '2',
      'username': '<PERSON>',
      'handle': '@david_s',
      'profilePhoto': null,
      'isBlocked': true,
    },
    {
      '_id': '3',
      'username': '<PERSON>',
      'handle': '@emily_c',
      'profilePhoto': null,
      'isBlocked': true,
    },
    {
      '_id': '4',
      'username': '<PERSON>',
      'handle': '@robert_w',
      'profilePhoto': null,
      'isBlocked': true,
    },
    {
      '_id': '5',
      'username': 'Sarah <PERSON>',
      'handle': '@sarah_b',
      'profilePhoto': null,
      'isBlocked': true,
    },
    {
      '_id': '6',
      'username': 'Michael Johnson',
      'handle': '@michael_j',
      'profilePhoto': null,
      'isBlocked': true,
    },
    {
      '_id': '7',
      'username': 'Lisa Green',
      'handle': '@lisa_g',
      'profilePhoto': null,
      'isBlocked': true,
    },
    {
      '_id': '8',
      'username': 'Chris Taylor',
      'handle': '@chris_t',
      'profilePhoto': null,
      'isBlocked': true,
    },
    {
      '_id': '9',
      'username': 'Amanda Roberts',
      'handle': '@amanda_r',
      'profilePhoto': null,
      'isBlocked': true,
    },
    {
      '_id': '10',
      'username': 'Kevin Lee',
      'handle': '@kevin_l',
      'profilePhoto': null,
      'isBlocked': true,
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadBlockedUsers();
  }

  Future<void> _loadBlockedUsers() async {
    setState(() => _isLoading = true);
    
    try {
      // For now, use sample data. In production, you'd fetch from API
      // final response = await http.get(
      //   Uri.parse('http://192.168.1.9:3000/api/users/${widget.userId}/blocked'),
      // );
      
      // if (response.statusCode == 200) {
      //   final data = jsonDecode(response.body);
      //   setState(() {
      //     _blockedUsers = List<Map<String, dynamic>>.from(data['blockedUsers'] ?? []);
      //   });
      // }
      
      // Using sample data for demonstration
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() {
        _blockedUsers = _sampleBlockedUsers;
      });
      
    } catch (e) {
      print('Error loading blocked users: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _unblockUser(String userId, String username) async {
    try {
      // Show confirmation dialog
      final confirmed = await _showUnblockConfirmation(username);
      if (!confirmed) return;

      setState(() => _isLoading = true);

      // In production, you'd call the API
      // final response = await http.put(
      //   Uri.parse('http://192.168.1.9:3000/api/users/$userId/block'),
      //   headers: {'Content-Type': 'application/json'},
      //   body: jsonEncode({'isBlocked': false}),
      // );

      // if (response.statusCode == 200) {
      //   await _loadBlockedUsers();
      //   if (mounted) {
      //     ScaffoldMessenger.of(context).showSnackBar(
      //       SnackBar(
      //         content: Text('$username has been unblocked'),
      //         backgroundColor: Colors.green,
      //       ),
      //     );
      //   }
      // }

      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      setState(() {
        _blockedUsers.removeWhere((user) => user['_id'] == userId);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$username has been unblocked'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      print('Error unblocking user: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to unblock user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<bool> _showUnblockConfirmation(String username) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unblock User'),
        content: Text('Are you sure you want to unblock $username?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
            ),
            child: const Text('Unblock'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Blocked Users',
          style: AppTheme.headingMedium.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _blockedUsers.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  padding: const EdgeInsets.all(AppTheme.spacingM),
                  itemCount: _blockedUsers.length,
                  itemBuilder: (context, index) {
                    final user = _blockedUsers[index];
                    return _buildUserTile(user);
                  },
                ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.block,
            size: 64,
            color: AppTheme.textSecondary,
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            'No Blocked Users',
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            'You haven\'t blocked any users yet.',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUserTile(Map<String, dynamic> user) {
    final username = user['username'] ?? 'Unknown User';
    final handle = user['handle'] ?? '@unknown';
    final profilePhoto = user['profilePhoto'];

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: AppTheme.softShadow,
      ),
      child: Row(
        children: [
          // Profile Avatar
          CircleAvatar(
            radius: 24,
            backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
            backgroundImage: profilePhoto != null 
                ? MemoryImage(base64Decode(profilePhoto.split(',')[1]))
                : null,
            child: profilePhoto == null
                ? Text(
                    username.isNotEmpty ? username[0].toUpperCase() : 'U',
                    style: AppTheme.bodyLarge.copyWith(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          
          const SizedBox(width: AppTheme.spacingM),
          
          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  username,
                  style: AppTheme.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  handle,
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          // Unblock Button
          ElevatedButton(
            onPressed: () => _unblockUser(user['_id'], username),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
              foregroundColor: AppTheme.primaryColor,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingM,
                vertical: AppTheme.spacingS,
              ),
            ),
            child: const Text(
              'Unblock',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}
