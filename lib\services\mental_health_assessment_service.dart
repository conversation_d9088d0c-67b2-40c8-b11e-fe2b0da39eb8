import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/assessment_models.dart';
import 'assessment_questions_service.dart';
import 'enhanced_ai_analysis_service.dart';

class MentalHealthAssessmentService {
  static final MentalHealthAssessmentService _instance = MentalHealthAssessmentService._internal();
  factory MentalHealthAssessmentService() => _instance;
  MentalHealthAssessmentService._internal();

  final AssessmentQuestionsService _questionsService = AssessmentQuestionsService();
  final EnhancedAIAnalysisService _aiService = EnhancedAIAnalysisService();
  
  bool _isInitialized = false;

  /// Initialize the assessment service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _aiService.initialize();
      _isInitialized = true;
      debugPrint('✅ Mental Health Assessment Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing assessment service: $e');
    }
  }

  /// Process assessment responses and generate comprehensive results
  Future<AssessmentResult> processAssessment(List<AssessmentResponse> responses) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      debugPrint('🧠 Processing assessment with ${responses.length} responses');

      // Calculate category results
      final categoryResults = _calculateCategoryResults(responses);
      
      // Generate AI analysis from responses
      final aiAnalysis = await _generateAIAnalysis(responses);
      
      // Determine overall result
      final overallResult = _calculateOverallResult(categoryResults, aiAnalysis);
      
      // Generate recommendations
      final recommendations = _generateRecommendations(categoryResults, aiAnalysis);
      
      // Generate resources
      final resources = _generateResources(overallResult);
      
      // Assess risk level
      final riskAssessment = _assessRiskLevel(categoryResults, aiAnalysis);
      
      // Calculate confidence score
      final confidenceScore = _calculateConfidenceScore(responses, aiAnalysis);

      final result = AssessmentResult(
        id: _generateAssessmentId(),
        completedAt: DateTime.now(),
        categoryResults: categoryResults,
        overallResult: overallResult,
        recommendations: recommendations,
        resources: resources,
        requiresProfessionalHelp: riskAssessment['requiresProfessionalHelp'] as bool,
        riskLevel: riskAssessment['level'] as String,
        confidenceScore: confidenceScore,
        aiAnalysis: aiAnalysis,
      );

      debugPrint('✅ Assessment processed successfully');
      return result;
    } catch (e) {
      debugPrint('❌ Error processing assessment: $e');
      rethrow;
    }
  }

  /// Calculate results for each category
  Map<String, CategoryResult> _calculateCategoryResults(List<AssessmentResponse> responses) {
    final Map<String, CategoryResult> results = {};
    final categories = _questionsService.getCategories();

    for (final category in categories) {
      final categoryResponses = responses.where((r) => r.category == category).toList();
      if (categoryResponses.isEmpty) continue;

      final questions = _questionsService.getQuestionsByCategory(category);
      final totalScore = categoryResponses.fold(0, (sum, response) => sum + response.score);
      final maxScore = questions.fold(0, (sum, question) => 
        sum + (question.options.map((o) => o.score).reduce(max) * question.weight));

      final percentage = maxScore > 0 ? (totalScore / maxScore) * 100 : 0.0;
      final severity = _determineSeverity(percentage);
      
      results[category] = CategoryResult(
        category: category,
        totalScore: totalScore,
        maxScore: maxScore,
        percentage: percentage,
        severity: severity,
        description: _getCategoryDescription(category, severity),
        symptoms: _identifySymptoms(category, categoryResponses),
        recommendations: _getCategoryRecommendations(category, severity),
      );
    }

    return results;
  }

  /// Generate AI analysis from assessment responses
  Future<Map<String, dynamic>> _generateAIAnalysis(List<AssessmentResponse> responses) async {
    try {
      // Convert responses to narrative text for AI analysis
      final narrative = _convertResponsesToNarrative(responses);
      
      // Get AI analysis
      final aiAnalysis = await _aiService.performEnhancedAnalysis(narrative);
      
      return aiAnalysis;
    } catch (e) {
      debugPrint('❌ Error in AI analysis: $e');
      return {};
    }
  }

  /// Convert assessment responses to narrative text for AI processing
  String _convertResponsesToNarrative(List<AssessmentResponse> responses) {
    final List<String> narrativeParts = [];
    final questions = _questionsService.getAllQuestions();

    for (final response in responses) {
      final question = questions.firstWhere((q) => q.id == response.questionId);
      final option = question.options.firstWhere((o) => o.id == response.optionId);
      
      // Create narrative based on response severity
      if (response.score > 0) {
        switch (response.category) {
          case 'anxiety':
            if (response.score >= 2) {
              narrativeParts.add('I frequently feel anxious and worried');
            } else {
              narrativeParts.add('I sometimes feel nervous or on edge');
            }
            break;
          case 'depression':
            if (response.score >= 2) {
              narrativeParts.add('I often feel sad, hopeless, and have little interest in activities');
            } else {
              narrativeParts.add('I sometimes feel down or depressed');
            }
            break;
          case 'stress':
            if (response.score >= 2) {
              narrativeParts.add('I feel overwhelmed and unable to cope with daily pressures');
            } else {
              narrativeParts.add('I experience some stress in my daily life');
            }
            break;
          case 'ptsd':
            if (response.score >= 2) {
              narrativeParts.add('I have disturbing memories and avoid reminders of traumatic experiences');
            }
            break;
          case 'bipolar':
            if (response.score >= 1) {
              narrativeParts.add('I experience extreme mood swings and periods of unusual energy');
            }
            break;
          case 'ocd':
            if (response.score >= 2) {
              narrativeParts.add('I have unwanted thoughts and feel compelled to repeat certain behaviors');
            }
            break;
        }
      }
    }

    return narrativeParts.join('. ') + '.';
  }

  /// Calculate overall assessment result
  OverallResult _calculateOverallResult(Map<String, CategoryResult> categoryResults, Map<String, dynamic> aiAnalysis) {
    // Find primary and secondary conditions based on scores
    final sortedCategories = categoryResults.entries.toList()
      ..sort((a, b) => b.value.percentage.compareTo(a.value.percentage));

    final primaryCondition = sortedCategories.isNotEmpty ? sortedCategories.first.key : 'none';
    final secondaryCondition = sortedCategories.length > 1 ? sortedCategories[1].key : 'none';

    // Determine overall severity
    final highestPercentage = sortedCategories.isNotEmpty ? sortedCategories.first.value.percentage : 0.0;
    final overallSeverity = _determineSeverity(highestPercentage);

    // Generate summary
    final summary = _generateSummary(categoryResults, aiAnalysis);
    
    // Extract key findings
    final keyFindings = _extractKeyFindings(categoryResults, aiAnalysis);
    
    // Check if needs immediate attention
    final needsImmediateAttention = _checkImmediateAttention(categoryResults, aiAnalysis);

    return OverallResult(
      primaryCondition: primaryCondition,
      secondaryCondition: secondaryCondition,
      overallSeverity: overallSeverity,
      summary: summary,
      keyFindings: keyFindings,
      needsImmediateAttention: needsImmediateAttention,
    );
  }

  /// Generate comprehensive recommendations
  List<String> _generateRecommendations(Map<String, CategoryResult> categoryResults, Map<String, dynamic> aiAnalysis) {
    final Set<String> recommendations = {};

    // Add AI-generated recommendations
    if (aiAnalysis.containsKey('recommendations')) {
      final aiRecommendations = aiAnalysis['recommendations'] as List<String>? ?? [];
      recommendations.addAll(aiRecommendations);
    }

    // Add category-specific recommendations
    for (final result in categoryResults.values) {
      if (result.percentage > 30) { // Only for moderate+ severity
        recommendations.addAll(result.recommendations);
      }
    }

    // Add general wellness recommendations
    recommendations.addAll([
      'Practice regular self-care activities',
      'Maintain a consistent sleep schedule',
      'Engage in regular physical exercise',
      'Consider mindfulness or meditation practices',
    ]);

    return recommendations.take(8).toList(); // Limit to top 8 recommendations
  }

  /// Generate relevant resources
  List<String> _generateResources(OverallResult overallResult) {
    final List<String> resources = [];

    // Add condition-specific resources
    switch (overallResult.primaryCondition) {
      case 'anxiety':
        resources.addAll([
          'Anxiety and Depression Association of America (ADAA)',
          'Calm app for guided meditation',
          'Headspace for anxiety management',
        ]);
        break;
      case 'depression':
        resources.addAll([
          'National Suicide Prevention Lifeline: 988',
          'Depression and Bipolar Support Alliance (DBSA)',
          'Crisis Text Line: Text HOME to 741741',
        ]);
        break;
      case 'stress':
        resources.addAll([
          'American Psychological Association stress resources',
          'Workplace stress management guides',
          'Stress reduction mobile apps',
        ]);
        break;
      case 'ptsd':
        resources.addAll([
          'National Center for PTSD',
          'PTSD Coach mobile app',
          'Veterans Crisis Line: **************',
        ]);
        break;
    }

    // Add general resources
    resources.addAll([
      'Psychology Today therapist finder',
      'National Alliance on Mental Illness (NAMI)',
      'Mental Health America resources',
    ]);

    return resources;
  }

  /// Assess risk level and need for professional help
  Map<String, dynamic> _assessRiskLevel(Map<String, CategoryResult> categoryResults, Map<String, dynamic> aiAnalysis) {
    String riskLevel = 'low';
    bool requiresProfessionalHelp = false;

    // Check AI analysis risk assessment
    if (aiAnalysis.containsKey('riskLevel')) {
      final aiRiskLevel = aiAnalysis['riskLevel'] as Map<String, dynamic>? ?? {};
      final aiLevel = aiRiskLevel['level'] as String? ?? 'low';
      
      if (aiLevel == 'critical' || aiLevel == 'high') {
        riskLevel = aiLevel;
        requiresProfessionalHelp = true;
      }
    }

    // Check category results for high severity
    for (final result in categoryResults.values) {
      if (result.severity == 'severe' || result.severity == 'critical') {
        riskLevel = 'high';
        requiresProfessionalHelp = true;
      }
      
      // Special check for depression with suicidal ideation
      if (result.category == 'depression' && result.percentage > 60) {
        riskLevel = 'critical';
        requiresProfessionalHelp = true;
      }
    }

    return {
      'level': riskLevel,
      'requiresProfessionalHelp': requiresProfessionalHelp,
    };
  }

  /// Calculate confidence score for the assessment
  double _calculateConfidenceScore(List<AssessmentResponse> responses, Map<String, dynamic> aiAnalysis) {
    double baseConfidence = 0.7; // Base confidence
    
    // Increase confidence based on number of responses
    final responseBonus = (responses.length / 20.0).clamp(0.0, 0.2);
    
    // AI analysis confidence
    final aiConfidence = aiAnalysis['confidenceScore'] as double? ?? 0.8;
    
    return (baseConfidence + responseBonus + (aiConfidence * 0.1)).clamp(0.0, 1.0);
  }

  /// Helper methods
  String _determineSeverity(double percentage) {
    if (percentage >= 80) return 'critical';
    if (percentage >= 60) return 'severe';
    if (percentage >= 40) return 'moderate';
    if (percentage >= 20) return 'mild';
    return 'minimal';
  }

  String _getCategoryDescription(String category, String severity) {
    final descriptions = {
      'anxiety': {
        'minimal': 'Little to no anxiety symptoms present',
        'mild': 'Some anxiety symptoms that are manageable',
        'moderate': 'Noticeable anxiety affecting daily activities',
        'severe': 'Significant anxiety requiring professional attention',
        'critical': 'Severe anxiety requiring immediate intervention',
      },
      'depression': {
        'minimal': 'No significant depressive symptoms',
        'mild': 'Some depressive symptoms present',
        'moderate': 'Moderate depression affecting daily life',
        'severe': 'Significant depression requiring treatment',
        'critical': 'Severe depression requiring immediate help',
      },
      // Add more categories as needed
    };

    return descriptions[category]?[severity] ?? 'Assessment results for $category';
  }

  List<String> _identifySymptoms(String category, List<AssessmentResponse> responses) {
    final List<String> symptoms = [];
    final questions = _questionsService.getQuestionsByCategory(category);

    for (final response in responses) {
      if (response.score >= 2) { // Moderate+ responses
        final question = questions.firstWhere((q) => q.id == response.questionId);
        symptoms.add(_extractSymptomFromQuestion(question.question));
      }
    }

    return symptoms;
  }

  String _extractSymptomFromQuestion(String question) {
    // Simplified symptom extraction
    if (question.contains('anxious') || question.contains('nervous')) return 'Anxiety';
    if (question.contains('depressed') || question.contains('hopeless')) return 'Depression';
    if (question.contains('overwhelmed')) return 'Feeling overwhelmed';
    if (question.contains('sleep')) return 'Sleep difficulties';
    if (question.contains('energy')) return 'Low energy';
    return 'General distress';
  }

  List<String> _getCategoryRecommendations(String category, String severity) {
    final recommendations = {
      'anxiety': [
        'Practice deep breathing exercises',
        'Try progressive muscle relaxation',
        'Consider cognitive behavioral therapy (CBT)',
        'Limit caffeine intake',
      ],
      'depression': [
        'Engage in regular physical activity',
        'Maintain social connections',
        'Consider therapy or counseling',
        'Practice gratitude journaling',
      ],
      'stress': [
        'Practice stress management techniques',
        'Set healthy boundaries',
        'Take regular breaks',
        'Consider time management strategies',
      ],
    };

    return recommendations[category] ?? ['Seek professional guidance'];
  }

  String _generateSummary(Map<String, CategoryResult> categoryResults, Map<String, dynamic> aiAnalysis) {
    final sortedCategories = categoryResults.entries.toList()
      ..sort((a, b) => b.value.percentage.compareTo(a.value.percentage));

    if (sortedCategories.isEmpty) {
      return 'Assessment completed with no significant concerns identified.';
    }

    final primary = sortedCategories.first;
    return 'Assessment indicates ${primary.value.severity} levels of ${primary.key} symptoms. ${primary.value.description}';
  }

  List<String> _extractKeyFindings(Map<String, CategoryResult> categoryResults, Map<String, dynamic> aiAnalysis) {
    final List<String> findings = [];

    for (final result in categoryResults.values) {
      if (result.percentage > 40) {
        findings.add('${result.severity.toUpperCase()} ${result.category} symptoms detected');
      }
    }

    if (aiAnalysis.containsKey('keyThemes')) {
      final themes = aiAnalysis['keyThemes'] as List<String>? ?? [];
      findings.addAll(themes.map((theme) => 'Key theme: $theme'));
    }

    return findings.take(5).toList();
  }

  bool _checkImmediateAttention(Map<String, CategoryResult> categoryResults, Map<String, dynamic> aiAnalysis) {
    // Check for critical severity in any category
    for (final result in categoryResults.values) {
      if (result.severity == 'critical') return true;
    }

    // Check AI risk assessment
    if (aiAnalysis.containsKey('riskLevel')) {
      final riskLevel = aiAnalysis['riskLevel'] as Map<String, dynamic>? ?? {};
      final level = riskLevel['level'] as String? ?? 'low';
      if (level == 'critical') return true;
    }

    return false;
  }

  String _generateAssessmentId() {
    return 'assessment_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }
}
