import 'package:flutter/services.dart';
import 'package:audioplayers/audioplayers.dart';
import 'dart:typed_data';
import 'dart:io';
import 'package:flutter/foundation.dart';

class AudioProcessingService {
  static final AudioProcessingService _instance = AudioProcessingService._internal();
  factory AudioProcessingService() => _instance;
  AudioProcessingService._internal();

  final Map<String, Uint8List> _audioCache = {};
  final Map<String, AudioFileInfo> _audioInfoCache = {};

  // Audio file mappings from sounds folder
  static const Map<String, String> soundFileMappings = {
    'Rain Sounds': 'sounds/calming-rain-257596.mp3',
    'Ocean Waves': 'sounds/ocean-waves-250310.mp3',
    'Forest Sounds': 'sounds/forest-nature-322637.mp3',
    'White Noise': 'sounds/white-noise-179828.mp3',
    'Fireplace': 'sounds/fireplace-loop-original-noise-178209.mp3',
    'Piano Music': 'sounds/peaceful-piano-303988.mp3',
    'Nature Birds': 'sounds/nature-birds-singing-217212.mp3',
    'Gentle Rain': 'sounds/gentle-rain-for-relaxation-and-sleep-337279.mp3',
    'Campfire': 'sounds/campfire-201656.mp3',
    'Waterfall': 'sounds/waterfall-176958.mp3',
    'Spring Forest': 'sounds/spring-forest-nature-332842.mp3',
    'Singing Bowl': 'sounds/singing-bowl-sing-33364.mp3',
    'Rain and Wind Chimes': 'sounds/rain-and-wind-chimes-314370.mp3',
    'Gentle Breeze': 'sounds/a-gentle-breeze-wind-4-14681.mp3',
    'Calm Nature': 'sounds/calm-nature-sounds-196258.mp3',
    'Light Rain': 'sounds/light-rain-109591.mp3',
    'Rain on Window': 'sounds/gentle-rain-on-window-350529.mp3',
    'Sleep Music': 'sounds/relaxing-sleep-music-with-soft-ambient-rain-369762.mp3',
  };

  /// Load and cache audio file from assets
  Future<Uint8List?> loadAudioFile(String soundName) async {
    try {
      final filePath = soundFileMappings[soundName];
      if (filePath == null) {
        debugPrint('❌ No file mapping found for sound: $soundName');
        return null;
      }

      // Check cache first
      if (_audioCache.containsKey(soundName)) {
        debugPrint('✅ Audio file loaded from cache: $soundName');
        return _audioCache[soundName];
      }

      // Load from assets
      final ByteData data = await rootBundle.load(filePath);
      final Uint8List bytes = data.buffer.asUint8List();
      
      // Cache the audio data
      _audioCache[soundName] = bytes;
      
      debugPrint('✅ Audio file loaded and cached: $soundName (${bytes.length} bytes)');
      return bytes;
    } catch (e) {
      debugPrint('❌ Error loading audio file $soundName: $e');
      return null;
    }
  }

  /// Get audio file information
  Future<AudioFileInfo?> getAudioInfo(String soundName) async {
    try {
      // Check cache first
      if (_audioInfoCache.containsKey(soundName)) {
        return _audioInfoCache[soundName];
      }

      final audioData = await loadAudioFile(soundName);
      if (audioData == null) return null;

      // Create audio info (simplified - in real implementation you'd parse the audio file)
      final info = AudioFileInfo(
        name: soundName,
        filePath: soundFileMappings[soundName]!,
        sizeBytes: audioData.length,
        estimatedDuration: _estimateDuration(soundName),
        format: 'mp3',
        sampleRate: 44100, // Standard MP3 sample rate
        channels: 2, // Stereo
      );

      _audioInfoCache[soundName] = info;
      return info;
    } catch (e) {
      debugPrint('❌ Error getting audio info for $soundName: $e');
      return null;
    }
  }

  /// Apply basic audio processing (volume normalization, noise reduction simulation)
  Future<Uint8List?> processAudio(String soundName, AudioProcessingOptions options) async {
    try {
      final originalData = await loadAudioFile(soundName);
      if (originalData == null) return null;

      // For now, we'll simulate processing by returning the original data
      // In a real implementation, you would use audio processing libraries
      debugPrint('🔧 Processing audio: $soundName with options: ${options.toString()}');
      
      // Simulate processing delay
      await Future.delayed(const Duration(milliseconds: 100));
      
      return originalData;
    } catch (e) {
      debugPrint('❌ Error processing audio $soundName: $e');
      return null;
    }
  }

  /// Estimate duration based on sound name (fallback method)
  Duration _estimateDuration(String soundName) {
    // These are estimated durations - in real implementation you'd parse the audio file
    const Map<String, Duration> estimatedDurations = {
      'Rain Sounds': Duration(minutes: 30),
      'Ocean Waves': Duration(minutes: 45),
      'Forest Sounds': Duration(minutes: 35),
      'White Noise': Duration(minutes: 60),
      'Fireplace': Duration(minutes: 40),
      'Piano Music': Duration(minutes: 25),
      'Nature Birds': Duration(minutes: 20),
      'Gentle Rain': Duration(minutes: 30),
      'Campfire': Duration(minutes: 35),
      'Waterfall': Duration(minutes: 25),
      'Spring Forest': Duration(minutes: 30),
      'Singing Bowl': Duration(minutes: 15),
      'Rain and Wind Chimes': Duration(minutes: 40),
      'Gentle Breeze': Duration(minutes: 20),
      'Calm Nature': Duration(minutes: 35),
      'Light Rain': Duration(minutes: 25),
      'Rain on Window': Duration(minutes: 30),
      'Sleep Music': Duration(minutes: 50),
    };

    return estimatedDurations[soundName] ?? const Duration(minutes: 30);
  }

  /// Get all available sounds
  List<String> getAvailableSounds() {
    return soundFileMappings.keys.toList();
  }

  /// Clear audio cache to free memory
  void clearCache() {
    _audioCache.clear();
    _audioInfoCache.clear();
    debugPrint('🧹 Audio cache cleared');
  }

  /// Get cache size in bytes
  int getCacheSize() {
    return _audioCache.values.fold(0, (sum, data) => sum + data.length);
  }
}

class AudioFileInfo {
  final String name;
  final String filePath;
  final int sizeBytes;
  final Duration estimatedDuration;
  final String format;
  final int sampleRate;
  final int channels;

  AudioFileInfo({
    required this.name,
    required this.filePath,
    required this.sizeBytes,
    required this.estimatedDuration,
    required this.format,
    required this.sampleRate,
    required this.channels,
  });

  @override
  String toString() {
    return 'AudioFileInfo(name: $name, size: ${(sizeBytes / 1024 / 1024).toStringAsFixed(2)}MB, duration: ${estimatedDuration.inMinutes}min)';
  }
}

class AudioProcessingOptions {
  final double volumeGain;
  final bool noiseReduction;
  final bool normalize;
  final double fadeInDuration;
  final double fadeOutDuration;

  const AudioProcessingOptions({
    this.volumeGain = 1.0,
    this.noiseReduction = false,
    this.normalize = true,
    this.fadeInDuration = 0.0,
    this.fadeOutDuration = 0.0,
  });

  @override
  String toString() {
    return 'AudioProcessingOptions(gain: $volumeGain, noiseReduction: $noiseReduction, normalize: $normalize)';
  }
}
