# Mindease Keystore Generation Script
# This script generates a keystore for signing the Mindease Android app

Write-Host "🔐 Generating Mindease App Keystore..." -ForegroundColor Green

# Check if Java keytool is available
try {
    $keytoolVersion = keytool -help 2>&1
    Write-Host "✅ Java keytool found" -ForegroundColor Green
} catch {
    Write-Host "❌ Java keytool not found. Please install Java JDK" -ForegroundColor Red
    exit 1
}

# Set keystore parameters
$keystoreName = "mindease-keystore.jks"
$keyAlias = "mindease-key"
$keystorePassword = "mindease123"
$keyPassword = "mindease123"
$validity = 10000  # days
$keysize = 2048

# Distinguished Name information
$dname = "CN=Mindease Mental Health App, OU=Development, O=Mindease, L=City, ST=State, C=US"

Write-Host "📋 Keystore Configuration:" -ForegroundColor Yellow
Write-Host "  Keystore Name: $keystoreName"
Write-Host "  Key Alias: $keyAlias"
Write-Host "  Validity: $validity days"
Write-Host "  Key Size: $keysize bits"
Write-Host ""

# Check if keystore already exists
if (Test-Path "android\app\$keystoreName") {
    Write-Host "⚠️  Keystore already exists at android\app\$keystoreName" -ForegroundColor Yellow
    $overwrite = Read-Host "Do you want to overwrite it? (y/N)"
    if ($overwrite -ne "y" -and $overwrite -ne "Y") {
        Write-Host "❌ Keystore generation cancelled" -ForegroundColor Red
        exit 0
    }
    Remove-Item "android\app\$keystoreName" -Force
}

# Generate the keystore
Write-Host "🔨 Generating keystore..." -ForegroundColor Blue

$keytoolCommand = @(
    "keytool"
    "-genkey"
    "-v"
    "-keystore", "android\app\$keystoreName"
    "-alias", $keyAlias
    "-keyalg", "RSA"
    "-keysize", $keysize
    "-validity", $validity
    "-storepass", $keystorePassword
    "-keypass", $keyPassword
    "-dname", $dname
)

try {
    & $keytoolCommand[0] $keytoolCommand[1..($keytoolCommand.Length-1)]
    
    if (Test-Path "android\app\$keystoreName") {
        Write-Host "✅ Keystore generated successfully!" -ForegroundColor Green
        Write-Host "📁 Location: android\app\$keystoreName" -ForegroundColor Green
        
        # Display keystore information
        Write-Host ""
        Write-Host "📊 Keystore Information:" -ForegroundColor Yellow
        keytool -list -v -keystore "android\app\$keystoreName" -storepass $keystorePassword
        
        Write-Host ""
        Write-Host "🔒 Security Reminders:" -ForegroundColor Red
        Write-Host "  1. Keep the keystore file secure and backed up"
        Write-Host "  2. Never commit the keystore to version control"
        Write-Host "  3. Store passwords securely (consider using environment variables)"
        Write-Host "  4. The keystore is required for all future app updates"
        
        Write-Host ""
        Write-Host "📝 Next Steps:" -ForegroundColor Cyan
        Write-Host "  1. Update android/key.properties with your keystore details"
        Write-Host "  2. Run 'flutter build apk --release' to build signed APK"
        Write-Host "  3. Test the APK on a device before distribution"
        
    } else {
        Write-Host "❌ Keystore generation failed" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "❌ Error generating keystore: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 Keystore generation completed!" -ForegroundColor Green
