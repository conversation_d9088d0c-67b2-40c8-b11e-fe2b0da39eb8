import 'package:flutter/material.dart';
import 'package:myapp/theme/app_theme.dart';
import 'models/assessment_models.dart';
import 'services/therapist_referral_service.dart';
import 'therapist_referral_screen.dart';

class AssessmentResultsScreen extends StatefulWidget {
  final AssessmentResult result;

  const AssessmentResultsScreen({super.key, required this.result});

  @override
  State<AssessmentResultsScreen> createState() => _AssessmentResultsScreenState();
}

class _AssessmentResultsScreenState extends State<AssessmentResultsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TherapistReferralService _referralService = TherapistReferralService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppTheme.backgroundGradient),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              _buildAppBar(),
              _buildOverallResultCard(),
              _buildTabBar(),
              Expanded(child: _buildTabContent()),
              _buildBottomActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppTheme.radiusXL),
          bottomRight: Radius.circular(AppTheme.radiusXL),
        ),
        boxShadow: AppTheme.mediumShadow,
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          ),
          const SizedBox(width: AppTheme.spacingS),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Assessment Results',
                  style: AppTheme.headingMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Completed ${_formatDate(widget.result.completedAt)}',
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            child: const Icon(Icons.assessment, color: Colors.white, size: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildOverallResultCard() {
    final result = widget.result.overallResult;
    final riskColor = _getRiskColor(widget.result.riskLevel);
    
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.mediumShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingS),
                decoration: BoxDecoration(
                  color: riskColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
                child: Icon(
                  _getRiskIcon(widget.result.riskLevel),
                  color: riskColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Overall Assessment',
                      style: AppTheme.headingSmall.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${result.overallSeverity.toUpperCase()} - ${result.primaryCondition.toUpperCase()}',
                      style: AppTheme.bodyLarge.copyWith(
                        color: riskColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingM,
                  vertical: AppTheme.spacingS,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
                child: Text(
                  '${(widget.result.confidenceScore * 100).toInt()}% Confidence',
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            result.summary,
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
          ),
          if (result.needsImmediateAttention) ...[
            const SizedBox(height: AppTheme.spacingM),
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingM),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(AppTheme.radiusM),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.red.shade600, size: 20),
                  const SizedBox(width: AppTheme.spacingS),
                  Expanded(
                    child: Text(
                      'This assessment indicates you may benefit from immediate professional support.',
                      style: AppTheme.bodySmall.copyWith(
                        color: Colors.red.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(AppTheme.radiusL),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary,
        labelStyle: AppTheme.bodySmall.copyWith(fontWeight: FontWeight.bold),
        tabs: const [
          Tab(icon: Icon(Icons.bar_chart), text: 'Categories'),
          Tab(icon: Icon(Icons.lightbulb), text: 'Recommendations'),
          Tab(icon: Icon(Icons.local_hospital), text: 'Resources'),
          Tab(icon: Icon(Icons.psychology), text: 'AI Analysis'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildCategoriesTab(),
          _buildRecommendationsTab(),
          _buildResourcesTab(),
          _buildAIAnalysisTab(),
        ],
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return ListView(
      children: widget.result.categoryResults.entries.map((entry) {
        final category = entry.key;
        final result = entry.value;
        return _buildCategoryCard(category, result);
      }).toList(),
    );
  }

  Widget _buildCategoryCard(String category, CategoryResult result) {
    final severityColor = _getSeverityColor(result.severity);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                category.toUpperCase(),
                style: AppTheme.headingSmall.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingS,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: severityColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppTheme.radiusS),
                ),
                child: Text(
                  result.severity.toUpperCase(),
                  style: AppTheme.bodySmall.copyWith(
                    color: severityColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingS),
          LinearProgressIndicator(
            value: result.percentage / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(severityColor),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            '${result.percentage.toInt()}% - ${result.description}',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
          ),
          if (result.symptoms.isNotEmpty) ...[
            const SizedBox(height: AppTheme.spacingS),
            Text(
              'Key symptoms: ${result.symptoms.join(', ')}',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRecommendationsTab() {
    return ListView(
      children: [
        ...widget.result.recommendations.map((recommendation) => Container(
          margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
          padding: const EdgeInsets.all(AppTheme.spacingM),
          decoration: BoxDecoration(
            gradient: AppTheme.cardGradient,
            borderRadius: BorderRadius.circular(AppTheme.radiusL),
            boxShadow: AppTheme.softShadow,
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: AppTheme.secondaryColor, size: 20),
              const SizedBox(width: AppTheme.spacingS),
              Expanded(
                child: Text(
                  recommendation,
                  style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
                ),
              ),
            ],
          ),
        )),
        const SizedBox(height: AppTheme.spacingM),
        _buildCopingStrategiesCard(),
      ],
    );
  }

  Widget _buildCopingStrategiesCard() {
    final strategies = _referralService.getCopingStrategies(widget.result);
    
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.psychology, color: AppTheme.primaryColor, size: 24),
              const SizedBox(width: AppTheme.spacingS),
              Text(
                'Immediate Coping Strategies',
                style: AppTheme.headingSmall.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),
          ...strategies.take(5).map((strategy) => Padding(
            padding: const EdgeInsets.only(bottom: AppTheme.spacingS),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.arrow_right, color: AppTheme.secondaryColor, size: 16),
                const SizedBox(width: AppTheme.spacingS),
                Expanded(
                  child: Text(
                    strategy,
                    style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildResourcesTab() {
    return ListView(
      children: [
        ...widget.result.resources.map((resource) => Container(
          margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
          padding: const EdgeInsets.all(AppTheme.spacingM),
          decoration: BoxDecoration(
            gradient: AppTheme.cardGradient,
            borderRadius: BorderRadius.circular(AppTheme.radiusL),
            boxShadow: AppTheme.softShadow,
          ),
          child: Row(
            children: [
              Icon(Icons.link, color: AppTheme.primaryColor, size: 20),
              const SizedBox(width: AppTheme.spacingS),
              Expanded(
                child: Text(
                  resource,
                  style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
                ),
              ),
            ],
          ),
        )),
        const SizedBox(height: AppTheme.spacingM),
        _buildEmergencyResourcesCard(),
      ],
    );
  }

  Widget _buildEmergencyResourcesCard() {
    final emergencyResources = _referralService.getEmergencyResources();
    
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.emergency, color: Colors.red.shade600, size: 24),
              const SizedBox(width: AppTheme.spacingS),
              Text(
                'Emergency Resources',
                style: AppTheme.headingSmall.copyWith(
                  color: Colors.red.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),
          ...emergencyResources.take(3).map((resource) => Container(
            margin: const EdgeInsets.only(bottom: AppTheme.spacingS),
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  resource.name,
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                  ),
                ),
                Text(
                  resource.phoneNumber,
                  style: AppTheme.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                Text(
                  resource.description,
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildAIAnalysisTab() {
    final aiAnalysis = widget.result.aiAnalysis;
    
    return ListView(
      children: [
        if (aiAnalysis.containsKey('mentalHealthStatus'))
          _buildAIAnalysisCard('Mental Health Classification', aiAnalysis['mentalHealthStatus']),
        if (aiAnalysis.containsKey('sentimentScore'))
          _buildAIAnalysisCard('Sentiment Analysis', aiAnalysis['sentimentScore']),
        if (aiAnalysis.containsKey('emotionalIndicators'))
          _buildAIAnalysisCard('Emotional Indicators', aiAnalysis['emotionalIndicators']),
        if (aiAnalysis.containsKey('riskLevel'))
          _buildAIAnalysisCard('Risk Assessment', aiAnalysis['riskLevel']),
      ],
    );
  }

  Widget _buildAIAnalysisCard(String title, dynamic data) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            data.toString(),
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        children: [
          if (widget.result.requiresProfessionalHelp)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _navigateToTherapistReferral(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.local_hospital),
                    const SizedBox(width: AppTheme.spacingS),
                    Text(
                      'Find Professional Help',
                      style: AppTheme.bodyLarge.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          const SizedBox(height: AppTheme.spacingS),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
                side: BorderSide(color: AppTheme.primaryColor),
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
              ),
              child: Text(
                'Back to Assessment',
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToTherapistReferral() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TherapistReferralScreen(assessmentResult: widget.result),
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  Color _getRiskColor(String riskLevel) {
    switch (riskLevel.toLowerCase()) {
      case 'critical':
        return Colors.red.shade600;
      case 'high':
        return Colors.orange.shade600;
      case 'medium':
        return Colors.yellow.shade700;
      case 'low':
        return Colors.green.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  IconData _getRiskIcon(String riskLevel) {
    switch (riskLevel.toLowerCase()) {
      case 'critical':
        return Icons.error;
      case 'high':
        return Icons.warning;
      case 'medium':
        return Icons.info;
      case 'low':
        return Icons.check_circle;
      default:
        return Icons.help;
    }
  }

  Color _getSeverityColor(String severity) {
    switch (severity.toLowerCase()) {
      case 'critical':
        return Colors.red.shade600;
      case 'severe':
        return Colors.orange.shade600;
      case 'moderate':
        return Colors.yellow.shade700;
      case 'mild':
        return Colors.blue.shade600;
      case 'minimal':
        return Colors.green.shade600;
      default:
        return Colors.grey.shade600;
    }
  }
}
