import 'package:flutter/material.dart';
import 'package:myapp/services/enhanced_ai_analysis_service.dart';

void main() {
  runApp(DatasetTestApp());
}

class DatasetTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Dataset Connection Test',
      home: DatasetTestScreen(),
    );
  }
}

class DatasetTestScreen extends StatefulWidget {
  @override
  _DatasetTestScreenState createState() => _DatasetTestScreenState();
}

class _DatasetTestScreenState extends State<DatasetTestScreen> {
  final EnhancedAIAnalysisService _aiService = EnhancedAIAnalysisService();
  bool _isLoading = false;
  String _testResults = '';

  @override
  void initState() {
    super.initState();
    _testDatasetConnection();
  }

  Future<void> _testDatasetConnection() async {
    setState(() {
      _isLoading = true;
      _testResults = 'Initializing AI Analysis Service...\n';
    });

    try {
      // Initialize the service
      await _aiService.initialize();
      
      setState(() {
        _testResults += '✅ AI Analysis Service initialized\n';
      });

      // Test with anxiety-related text
      final anxietyText = "I'm having panic attacks and feel overwhelmed with constant worry about everything";
      final anxietyAnalysis = await _aiService.performEnhancedAnalysis(anxietyText);
      
      setState(() {
        _testResults += '\n🧠 Testing Anxiety Analysis:\n';
        _testResults += 'Input: "$anxietyText"\n';
        _testResults += 'Primary Classification: ${anxietyAnalysis['mentalHealthStatus']['primary']}\n';
        _testResults += 'Confidence: ${anxietyAnalysis['mentalHealthStatus']['confidence']}\n';
        _testResults += 'Risk Level: ${anxietyAnalysis['riskLevel']['level']}\n';
        _testResults += 'Recommendations: ${(anxietyAnalysis['recommendations'] as List).take(2).join(', ')}\n';
      });

      // Test with depression-related text
      final depressionText = "I feel hopeless and worthless, like nothing matters anymore";
      final depressionAnalysis = await _aiService.performEnhancedAnalysis(depressionText);
      
      setState(() {
        _testResults += '\n🧠 Testing Depression Analysis:\n';
        _testResults += 'Input: "$depressionText"\n';
        _testResults += 'Primary Classification: ${depressionAnalysis['mentalHealthStatus']['primary']}\n';
        _testResults += 'Confidence: ${depressionAnalysis['mentalHealthStatus']['confidence']}\n';
        _testResults += 'Risk Level: ${depressionAnalysis['riskLevel']['level']}\n';
        _testResults += 'Recommendations: ${(depressionAnalysis['recommendations'] as List).take(2).join(', ')}\n';
      });

      // Test with positive text
      final positiveText = "I feel amazing and grateful for all the wonderful things in my life";
      final positiveAnalysis = await _aiService.performEnhancedAnalysis(positiveText);
      
      setState(() {
        _testResults += '\n🧠 Testing Positive Analysis:\n';
        _testResults += 'Input: "$positiveText"\n';
        _testResults += 'Primary Classification: ${positiveAnalysis['mentalHealthStatus']['primary']}\n';
        _testResults += 'Confidence: ${positiveAnalysis['mentalHealthStatus']['confidence']}\n';
        _testResults += 'Risk Level: ${positiveAnalysis['riskLevel']['level']}\n';
      });

      setState(() {
        _testResults += '\n✅ Dataset connection test completed successfully!\n';
        _testResults += '\n📊 The AI analysis is now using the comprehensive mental health datasets with 10,000+ examples for improved accuracy.';
      });

    } catch (e) {
      setState(() {
        _testResults += '\n❌ Error during dataset test: $e\n';
        _testResults += '\nThis might indicate that the datasets are not properly loaded or there\'s a parsing issue.';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Dataset Connection Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Mindease AI Dataset Connection Test',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'This test verifies that the comprehensive mental health datasets are properly connected and being used by the AI analysis service.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 24),
            if (_isLoading)
              Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Testing dataset connection...'),
                  ],
                ),
              ),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResults.isEmpty ? 'Test results will appear here...' : _testResults,
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _testDatasetConnection,
                  child: Text('Run Test Again'),
                ),
                SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _testResults = '';
                    });
                  },
                  child: Text('Clear Results'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Instructions to run this test:
// 1. Save this file as test_dataset_connection.dart in the lib folder
// 2. Run: flutter run test_dataset_connection.dart
// 3. This will open a test app that verifies dataset connection
// 4. Check the console output for detailed parsing information
