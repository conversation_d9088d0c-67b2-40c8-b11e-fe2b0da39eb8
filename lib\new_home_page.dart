import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/user_appointments_screen.dart';
import 'package:myapp/screens/location_therapist_finder.dart';
import 'package:myapp/mood_tracking.dart';
import 'package:myapp/journaling.dart';
import 'package:myapp/ai_analysis_screen.dart';
import 'package:myapp/meditation_screen.dart';
import 'package:myapp/mental_health_assessment_screen.dart';
import 'package:myapp/components/modern_bottom_nav.dart';
import 'package:myapp/settings.dart';
import 'package:myapp/enhanced_journal_entry_screen.dart';

class NewHomeScreen extends StatefulWidget {
  final String userId;

  const NewHomeScreen({super.key, required this.userId});

  @override
  State<NewHomeScreen> createState() => _NewHomeScreenState();
}

class _NewHomeScreenState extends State<NewHomeScreen> {
  Map<String, dynamic>? userDetails;
  List<dynamic> recentAppointments = [];
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      await Future.wait([
        _fetchUserDetails(),
        _fetchRecentAppointments(),
      ]);
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load data. Please try again.';
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _fetchUserDetails() async {
    try {
      final response = await http.get(
        Uri.parse('http://192.168.1.9:3000/api/users/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        setState(() {
          userDetails = responseData is Map
              ? (responseData['user'] ?? responseData)
              : responseData;
        });
      }
    } catch (e) {
      print('Error fetching user details: $e');
    }
  }

  Future<void> _fetchRecentAppointments() async {
    try {
      final response = await http.get(
        Uri.parse(
            'http://192.168.1.9:3000/api/appointments/user/${widget.userId}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> appointments = jsonDecode(response.body);
        setState(() {
          recentAppointments = appointments.take(3).toList();
        });
      }
    } catch (e) {
      print('Error fetching appointments: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScreenWithBottomNav(
      currentIndex: 0,
      userId: widget.userId,
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          automaticallyImplyLeading: false,
          flexibleSpace: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF6B73FF),
                  Color(0xFF9B59B6),
                  Color(0xFF8E44AD),
                ],
              ),
            ),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  border: Border.all(color: Colors.white.withOpacity(0.3)),
                ),
                child: const Icon(
                  Icons.psychology_outlined,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingM),
              const Text(
                "MindEase",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
          // Removed notification and settings icons as requested
        ),
        body: isLoading
            ? const Center(child: CircularProgressIndicator())
            : errorMessage != null
                ? _buildErrorState()
                : _buildHomeContent(),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            errorMessage!,
            style: AppTheme.bodyLarge.copyWith(color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingL),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildHomeContent() {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(AppTheme.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTodaySection(),
            const SizedBox(height: AppTheme.spacingL),
            _buildMoodCheckIn(),
            const SizedBox(height: AppTheme.spacingL),
            _buildRecentActivity(),
            const SizedBox(height: AppTheme.spacingL),
            _buildQuickAccessFeatures(),
            const SizedBox(height: 100), // Bottom padding for navigation
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final username = userDetails?['username'] ?? 'Friend';
    final currentHour = DateTime.now().hour;
    String greeting = 'Good Morning';
    String emoji = '🌅';

    if (currentHour >= 12 && currentHour < 17) {
      greeting = 'Good Afternoon';
      emoji = '☀️';
    } else if (currentHour >= 17) {
      greeting = 'Good Evening';
      emoji = '🌙';
    }

    final quotes = [
      "How are you feeling today? Remember, taking care of your mental health is just as important as your physical health. 💙",
      "Every day is a new opportunity to prioritize your well-being. You've got this! 🌟",
      "Your mental health matters. Take a moment to breathe and be kind to yourself. 🌸",
      "Progress, not perfection. Every small step towards better mental health counts. 🦋",
      "You are stronger than you think, braver than you feel, and more loved than you know. 💜",
    ];

    final quote = quotes[DateTime.now().day % quotes.length];

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF6B73FF),
            Color(0xFF9B59B6),
            Color(0xFF8E44AD),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                emoji,
                style: const TextStyle(fontSize: 32),
              ),
              const SizedBox(width: AppTheme.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "$greeting,",
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      "$username!",
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            quote,
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodaySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today',
          style: AppTheme.headingLarge.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildMoodCheckIn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'How are you feeling?',
          style: AppTheme.headingMedium.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppTheme.spacingM),
        Wrap(
          spacing: AppTheme.spacingS,
          runSpacing: AppTheme.spacingS,
          children: [
            _buildMoodButton(
                'Happy', Icons.sentiment_very_satisfied, Colors.green),
            _buildMoodButton(
                'Sad', Icons.sentiment_very_dissatisfied, Colors.blue),
            _buildMoodButton(
                'Anxious', Icons.sentiment_dissatisfied, Colors.orange),
            _buildMoodButton('Calm', Icons.sentiment_satisfied, Colors.teal),
            _buildMoodButton('Energetic', Icons.bolt, Colors.purple),
          ],
        ),
      ],
    );
  }

  Widget _buildMoodButton(String mood, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: InkWell(
        onTap: () => _recordMood(mood),
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingM,
            vertical: AppTheme.spacingS,
          ),
          child: Text(
            mood,
            style: AppTheme.bodyMedium.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: AppTheme.headingMedium.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppTheme.spacingM),
        // Real-time activity from backend
        FutureBuilder<List<Map<String, dynamic>>>(
          future: _fetchRecentActivity(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError ||
                !snapshot.hasData ||
                snapshot.data!.isEmpty) {
              return Column(
                children: [
                  _buildActivityItem(
                    icon: Icons.mood,
                    title: 'Mood Entry',
                    subtitle: 'Track your mood today',
                    iconColor: Colors.green,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MoodTrackingScreen(),
                      ),
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingS),
                  _buildActivityItem(
                    icon: Icons.edit,
                    title: 'Journal Entry',
                    subtitle: 'Write your thoughts',
                    iconColor: Colors.blue,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            EnhancedJournalEntryScreen(userId: widget.userId),
                      ),
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingS),
                  _buildActivityItem(
                    icon: Icons.analytics,
                    title: 'AI Analysis',
                    subtitle: 'Get insights on your mental health',
                    iconColor: Colors.purple,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            AIAnalysisScreen(userId: widget.userId),
                      ),
                    ),
                  ),
                ],
              );
            }

            return Column(
              children: snapshot.data!
                  .map((activity) => Padding(
                        padding:
                            const EdgeInsets.only(bottom: AppTheme.spacingS),
                        child: _buildActivityItem(
                          icon: _getActivityIcon(activity['type']),
                          title: activity['title'],
                          subtitle: activity['subtitle'],
                          iconColor: _getActivityColor(activity['type']),
                          onTap: () => _handleActivityTap(activity),
                        ),
                      ))
                  .toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color iconColor,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.radiusM),
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppTheme.radiusM),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _recordMood(String mood) async {
    try {
      final response = await http.post(
        Uri.parse('http://192.168.1.9:3000/api/mood'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'userId': widget.userId,
          'mood': mood.toLowerCase(),
          'date': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 201) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Mood recorded as $mood'),
            backgroundColor: Colors.green,
          ),
        );
        // Refresh data to show the new mood entry
        _loadData();
      } else {
        throw Exception('Failed to record mood');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to record mood: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<List<Map<String, dynamic>>> _fetchRecentActivity() async {
    try {
      List<Map<String, dynamic>> activities = [];

      // Fetch recent mood entries
      try {
        final moodResponse = await http.get(
          Uri.parse('http://192.168.1.9:3000/api/mood/${widget.userId}'),
        );
        if (moodResponse.statusCode == 200) {
          final moodData = jsonDecode(moodResponse.body);
          if (moodData is List && moodData.isNotEmpty) {
            final latestMood = moodData.first;
            activities.add({
              'type': 'mood',
              'title': 'Mood Entry',
              'subtitle': 'You logged your mood as ${latestMood['mood']}',
              'data': latestMood,
            });
          }
        }
      } catch (e) {
        print('Error fetching mood data: $e');
      }

      // Fetch recent journal entries
      try {
        final journalResponse = await http.get(
          Uri.parse(
              'http://192.168.1.9:3000/api/journal/user/${widget.userId}'),
        );
        if (journalResponse.statusCode == 200) {
          final journalData = jsonDecode(journalResponse.body);
          if (journalData is List && journalData.isNotEmpty) {
            activities.add({
              'type': 'journal',
              'title': 'Journal Entry',
              'subtitle': 'Latest journal entry available',
              'data': journalData.first,
            });
          }
        }
      } catch (e) {
        print('Error fetching journal data: $e');
      }

      // Fetch AI analysis history
      try {
        final analysisResponse = await http.get(
          Uri.parse(
              'http://192.168.1.9:3000/api/ai-analysis/history/${widget.userId}'),
        );
        if (analysisResponse.statusCode == 200) {
          final analysisData = jsonDecode(analysisResponse.body);
          if (analysisData is List && analysisData.isNotEmpty) {
            activities.add({
              'type': 'analysis',
              'title': 'AI Analysis',
              'subtitle': 'AI analysis complete',
              'data': analysisData.first,
            });
          }
        }
      } catch (e) {
        print('Error fetching analysis data: $e');
      }

      return activities;
    } catch (e) {
      print('Error fetching recent activity: $e');
      return [];
    }
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'mood':
        return Icons.mood;
      case 'journal':
        return Icons.edit;
      case 'analysis':
        return Icons.analytics;
      default:
        return Icons.info;
    }
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'mood':
        return Colors.green;
      case 'journal':
        return Colors.blue;
      case 'analysis':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _handleActivityTap(Map<String, dynamic> activity) {
    switch (activity['type']) {
      case 'mood':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const MoodTrackingScreen(),
          ),
        );
        break;
      case 'journal':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                EnhancedJournalEntryScreen(userId: widget.userId),
          ),
        );
        break;
      case 'analysis':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AIAnalysisScreen(userId: widget.userId),
          ),
        );
        break;
    }
  }

  Widget _buildQuickAccessFeatures() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mental Health Tools',
          style: AppTheme.headingMedium.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppTheme.spacingM),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppTheme.spacingM,
          mainAxisSpacing: AppTheme.spacingM,
          childAspectRatio: 1.2,
          children: [
            _buildFeatureCard(
              'Mood Tracking',
              Icons.mood,
              'Track your daily mood',
              Colors.green,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MoodTrackingScreen(),
                ),
              ),
            ),
            _buildFeatureCard(
              'AI Analysis',
              Icons.psychology,
              'Get AI insights',
              Colors.blue,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AIAnalysisScreen(userId: widget.userId),
                ),
              ),
            ),
            _buildFeatureCard(
              'Meditation',
              Icons.self_improvement,
              'Guided meditation',
              AppTheme.primaryColor,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MeditationScreen(userId: widget.userId),
                ),
              ),
            ),
            _buildFeatureCard(
              'Assessment',
              Icons.assessment,
              'Mental health screening',
              Colors.purple,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MentalHealthAssessmentScreen(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureCard(
    String title,
    IconData icon,
    String subtitle,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppTheme.radiusM),
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppTheme.radiusM),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: color,
                size: 28,
              ),
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              title,
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  String _getTimeOfDay() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTheme.headingMedium.copyWith(color: AppTheme.textPrimary),
        ),
        const SizedBox(height: AppTheme.spacingM),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'Book Appointment',
                Icons.calendar_today,
                AppTheme.primaryColor,
                () => _navigateToTherapistList(),
              ),
            ),
            const SizedBox(width: AppTheme.spacingM),
            Expanded(
              child: _buildActionCard(
                'Emergency Help',
                Icons.emergency,
                AppTheme.errorColor,
                () => _showEmergencyDialog(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
      String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusM),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              title,
              style: AppTheme.bodyMedium.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMentalHealthTools() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mental Health Tools',
          style: AppTheme.headingMedium.copyWith(color: AppTheme.textPrimary),
        ),
        const SizedBox(height: AppTheme.spacingM),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppTheme.spacingM,
          mainAxisSpacing: AppTheme.spacingM,
          childAspectRatio: 1.2,
          children: [
            _buildToolCard(
              'Mood Tracking',
              Icons.mood,
              'Track your daily mood',
              AppTheme.successColor,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MoodTrackingScreen(),
                ),
              ),
            ),
            _buildToolCard(
              'Journaling',
              Icons.book,
              'Write your thoughts',
              AppTheme.warningColor,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => JournalScreen(userId: widget.userId),
                ),
              ),
            ),
            _buildToolCard(
              'AI Analysis',
              Icons.psychology,
              'Get AI insights',
              Colors.blue,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AIAnalysisScreen(userId: widget.userId),
                ),
              ),
            ),
            _buildToolCard(
              'Meditation',
              Icons.self_improvement,
              'Guided meditation',
              AppTheme.primaryColor,
              () => _showMeditationFeatures(),
            ),
            _buildToolCard(
              'Assessment',
              Icons.assessment,
              'Mental health screening',
              Colors.purple,
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MentalHealthAssessmentScreen(),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildToolCard(String title, IconData icon, String subtitle,
      Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingM),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusM),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppTheme.spacingS),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.radiusS),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            const SizedBox(height: AppTheme.spacingS),
            Text(
              title,
              style: AppTheme.bodyLarge.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentAppointments() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Appointments',
              style:
                  AppTheme.headingMedium.copyWith(color: AppTheme.textPrimary),
            ),
            TextButton(
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      UserAppointmentsScreen(userId: widget.userId),
                ),
              ),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: AppTheme.spacingM),
        recentAppointments.isEmpty
            ? _buildEmptyAppointments()
            : Column(
                children: recentAppointments
                    .map((appointment) => _buildAppointmentCard(appointment))
                    .toList(),
              ),
      ],
    );
  }

  Widget _buildEmptyAppointments() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingL),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.calendar_today_outlined,
            size: 48,
            color: AppTheme.textSecondary,
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            'No appointments yet',
            style: AppTheme.bodyLarge.copyWith(color: AppTheme.textSecondary),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Text(
            'Book your first appointment with a therapist',
            style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentCard(Map<String, dynamic> appointment) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusM),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusS),
            ),
            child: Icon(
              appointment['type'] == 'online' ? Icons.videocam : Icons.person,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(width: AppTheme.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  appointment['therapistEmail'] ?? 'Therapist',
                  style: AppTheme.bodyLarge.copyWith(
                    color: AppTheme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${appointment['date']} at ${appointment['time']}',
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingS,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              color: _getStatusColor(appointment['status']).withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusS),
            ),
            child: Text(
              appointment['status']?.toString().toUpperCase() ?? 'UNKNOWN',
              style: AppTheme.bodySmall.copyWith(
                color: _getStatusColor(appointment['status']),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'scheduled':
        return AppTheme.successColor;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
        return AppTheme.errorColor;
      default:
        return AppTheme.textSecondary;
    }
  }

  Widget _buildWellnessTips() {
    final tips = [
      'Take deep breaths when feeling anxious',
      'Practice gratitude daily',
      'Stay hydrated and get enough sleep',
      'Connect with friends and family',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Wellness Tips',
          style: AppTheme.headingMedium.copyWith(color: AppTheme.textPrimary),
        ),
        const SizedBox(height: AppTheme.spacingM),
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingM),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusM),
            border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: AppTheme.primaryColor,
                size: 24,
              ),
              const SizedBox(width: AppTheme.spacingM),
              Expanded(
                child: Text(
                  tips[DateTime.now().day % tips.length],
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textPrimary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showEmergencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.emergency, color: AppTheme.errorColor),
            const SizedBox(width: 8),
            const Text('Emergency Help'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'If you are in immediate danger or having thoughts of self-harm, please contact:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 16),
            _buildEmergencyContact('🚨 Emergency Services', '911', '911'),
            _buildEmergencyContact(
                '💬 Crisis Text Line', 'Text HOME to 741741', 'sms:741741'),
            _buildEmergencyContact(
                '📞 National Suicide Prevention', '988', 'tel:988'),
            _buildEmergencyContact(
                '🌐 Crisis Chat',
                'suicidepreventionlifeline.org',
                'https://suicidepreventionlifeline.org/chat/'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: AppTheme.warningColor.withOpacity(0.3)),
              ),
              child: const Text(
                '⚠️ You are not alone. Help is available 24/7.',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmergencyContact(String title, String subtitle, String action) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        dense: true,
        contentPadding: EdgeInsets.zero,
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          // Here you would implement URL launching for phone/web/sms
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Opening $title...')),
          );
        },
      ),
    );
  }

  void _showMeditationFeatures() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.self_improvement, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            const Text('Meditation & Mindfulness'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMeditationOption(
                '🧘‍♀️ Breathing Exercise', '5-minute guided breathing', () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MeditationScreen(userId: widget.userId),
                ),
              );
            }),
            _buildMeditationOption(
                '🌊 Calm Sounds', 'Nature sounds for relaxation', () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MeditationScreen(userId: widget.userId),
                ),
              );
            }),
            _buildMeditationOption(
                '💭 Mindfulness Tips', 'Daily mindfulness practices', () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MeditationScreen(userId: widget.userId),
                ),
              );
            }),
            _buildMeditationOption(
                '😴 Sleep Stories', 'Bedtime relaxation stories', () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MeditationScreen(userId: widget.userId),
                ),
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildMeditationOption(
      String title, String subtitle, VoidCallback onTap) {
    return ListTile(
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  void _navigateToTherapistList() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LocationTherapistFinder(
          userId: widget.userId,
        ),
      ),
    );
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.notifications, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            const Text('Notifications'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.schedule, color: Colors.blue),
              title: const Text('Appointment Reminder'),
              subtitle:
                  const Text('Your session with Dr. Smith is tomorrow at 2 PM'),
              trailing: const Text('1h ago'),
            ),
            ListTile(
              leading: const Icon(Icons.mood, color: Colors.green),
              title: const Text('Mood Check-in'),
              subtitle: const Text('How are you feeling today?'),
              trailing: const Text('3h ago'),
            ),
            ListTile(
              leading: const Icon(Icons.lightbulb, color: Colors.orange),
              title: const Text('Daily Tip'),
              subtitle: const Text('Try the 4-7-8 breathing technique'),
              trailing: const Text('1d ago'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _startBreathingExercise() {
    Navigator.pop(context); // Close meditation dialog first

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildBreathingExerciseDialog(),
    );
  }

  void _showCalmSounds() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🌊 Calm Sounds'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.waves),
              title: const Text('Ocean Waves'),
              trailing: IconButton(
                icon: const Icon(Icons.play_arrow),
                onPressed: () => _playSound('Ocean Waves'),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.park),
              title: const Text('Forest Sounds'),
              trailing: IconButton(
                icon: const Icon(Icons.play_arrow),
                onPressed: () => _playSound('Forest Sounds'),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.cloud),
              title: const Text('Rain Drops'),
              trailing: IconButton(
                icon: const Icon(Icons.play_arrow),
                onPressed: () => _playSound('Rain Drops'),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showMindfulnessTips() {
    final tips = [
      '🌱 Start your day with 5 minutes of mindful breathing',
      '🍃 Practice gratitude by listing 3 things you\'re thankful for',
      '🌸 Take mindful walks and notice your surroundings',
      '🧘‍♂️ Use the 5-4-3-2-1 grounding technique when anxious',
      '💫 Practice loving-kindness meditation before sleep',
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('💭 Mindfulness Tips'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: tips
              .map((tip) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Text(tip),
                  ))
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSleepStories() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('😴 Sleep Stories'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Peaceful Garden'),
              subtitle: const Text('15 min • Relaxing nature story'),
              trailing: const Icon(Icons.play_arrow),
              onTap: () => _playSleepStory('Peaceful Garden'),
            ),
            ListTile(
              title: const Text('Mountain Retreat'),
              subtitle: const Text('20 min • Calming mountain journey'),
              trailing: const Icon(Icons.play_arrow),
              onTap: () => _playSleepStory('Mountain Retreat'),
            ),
            ListTile(
              title: const Text('Ocean Sunset'),
              subtitle: const Text('12 min • Peaceful beach setting'),
              trailing: const Icon(Icons.play_arrow),
              onTap: () => _playSleepStory('Ocean Sunset'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  // Breathing Exercise Dialog
  Widget _buildBreathingExerciseDialog() {
    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: const Text('🧘‍♀️ Breathing Exercise'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('4-7-8 Breathing Technique'),
              const SizedBox(height: 20),
              const Text('• Inhale for 4 counts'),
              const Text('• Hold for 7 counts'),
              const Text('• Exhale for 8 counts'),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                          '🧘‍♀️ Breathing exercise started! Follow the rhythm.'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                child: const Text('Start Exercise'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  // Play Sound Method
  void _playSound(String soundName) {
    Navigator.pop(context); // Close calm sounds dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('🎵 Playing $soundName... Enjoy the relaxing sounds!'),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Play Sleep Story Method
  void _playSleepStory(String storyName) {
    Navigator.pop(context); // Close sleep stories dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('😴 Playing "$storyName"... Sweet dreams!'),
        backgroundColor: Colors.purple,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
