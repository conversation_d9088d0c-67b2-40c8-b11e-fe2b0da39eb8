# Mindease Android APK Build & Deployment Guide

This guide provides step-by-step instructions for building and deploying the Mindease mental health app with enhanced audio processing, AI analysis, and mood tracking integration.

## 🚀 Quick Start

### Prerequisites
- Flutter SDK (latest stable version)
- Android SDK and Android Studio
- Java JDK 8 or higher
- Git

### 1. Environment Setup

```bash
# Verify Flutter installation
flutter doctor

# Check Android toolchain
flutter doctor --android-licenses

# Ensure all dependencies are installed
flutter pub get
```

### 2. Generate Keystore (First Time Only)

```powershell
# Run the keystore generation script
.\generate-keystore.ps1
```

Or manually create keystore:
```bash
keytool -genkey -v -keystore android/app/mindease-keystore.jks -alias mindease-key -keyalg RSA -keysize 2048 -validity 10000
```

### 3. Configure Signing

Ensure `android/key.properties` contains:
```properties
storePassword=mindease123
keyPassword=mindease123
keyAlias=mindease-key
storeFile=mindease-keystore.jks
```

## 📱 Building the APK

### Debug Build (for testing)
```bash
flutter build apk --debug
```

### Release Build (for production)
```bash
# Clean previous builds
flutter clean
flutter pub get

# Build release APK with optimizations
flutter build apk --release --obfuscate --split-debug-info=build/debug-info

# Alternative: Build app bundle for Play Store
flutter build appbundle --release --obfuscate --split-debug-info=build/debug-info
```

### Build with Specific Configurations
```bash
# Build with specific flavor (if configured)
flutter build apk --release --flavor production

# Build for specific architecture
flutter build apk --release --target-platform android-arm64

# Build multiple APKs for different architectures
flutter build apk --release --split-per-abi
```

## 🔧 Advanced Build Options

### Performance Optimized Build
```bash
flutter build apk --release \
  --obfuscate \
  --split-debug-info=build/debug-info \
  --tree-shake-icons \
  --dart-define=FLUTTER_WEB_USE_SKIA=true
```

### Build with Custom Build Number
```bash
flutter build apk --release --build-number=1 --build-name=1.0.0
```

## 📋 Pre-Build Checklist

- [ ] All dependencies updated (`flutter pub upgrade`)
- [ ] Code analysis passed (`flutter analyze`)
- [ ] Tests passing (`flutter test`)
- [ ] Keystore generated and configured
- [ ] ProGuard rules updated
- [ ] AndroidManifest.xml permissions reviewed
- [ ] App icons and splash screens configured
- [ ] Backend API endpoints configured for production

## 🧪 Testing the APK

### Install on Device
```bash
# Install debug APK
flutter install

# Install specific APK file
adb install build/app/outputs/flutter-apk/app-release.apk

# Install and launch
adb install -r build/app/outputs/flutter-apk/app-release.apk && adb shell am start -n com.mindease.mentalhealth/.MainActivity
```

### Testing Checklist
- [ ] App launches successfully
- [ ] Audio playback works with all sound files
- [ ] AI analysis processes journal entries correctly
- [ ] Mood tracking saves and displays data
- [ ] Network requests work (login, data sync)
- [ ] Biometric authentication functions
- [ ] Camera and image picker work
- [ ] Location services work (if enabled)
- [ ] App handles offline scenarios gracefully

## 📦 Build Outputs

After successful build, find your APK at:
- **Release APK**: `build/app/outputs/flutter-apk/app-release.apk`
- **Debug APK**: `build/app/outputs/flutter-apk/app-debug.apk`
- **App Bundle**: `build/app/outputs/bundle/release/app-release.aab`

## 🚀 Deployment Options

### 1. Direct APK Distribution
```bash
# Copy APK to distribution folder
cp build/app/outputs/flutter-apk/app-release.apk ./mindease-v1.0.0.apk

# Generate SHA256 checksum for verification
certutil -hashfile mindease-v1.0.0.apk SHA256
```

### 2. Google Play Store Deployment
```bash
# Build app bundle for Play Store
flutter build appbundle --release --obfuscate --split-debug-info=build/debug-info

# Upload build/app/outputs/bundle/release/app-release.aab to Play Console
```

### 3. Internal Testing Distribution
```bash
# Build debug version for internal testing
flutter build apk --debug --flavor staging

# Or build release with debug symbols
flutter build apk --release --debug --split-debug-info=build/debug-info
```

## 🔍 Troubleshooting

### Common Build Issues

**Issue**: Keystore not found
```bash
# Solution: Ensure keystore is in correct location
ls android/app/mindease-keystore.jks
```

**Issue**: ProGuard errors
```bash
# Solution: Check ProGuard rules and add missing keep rules
flutter build apk --release --verbose
```

**Issue**: Out of memory during build
```bash
# Solution: Increase Gradle memory
export GRADLE_OPTS="-Xmx4g -XX:MaxPermSize=512m"
```

**Issue**: Plugin compatibility issues
```bash
# Solution: Update all plugins
flutter pub upgrade
flutter clean
flutter pub get
```

## 📊 Build Performance Tips

1. **Enable R8 optimization** (already configured)
2. **Use ProGuard rules** for code shrinking
3. **Enable resource shrinking** in build.gradle
4. **Optimize images** and assets
5. **Remove unused dependencies**
6. **Use `--split-per-abi`** for smaller APKs

## 🔐 Security Considerations

- Keep keystore file secure and backed up
- Use environment variables for sensitive data in CI/CD
- Enable code obfuscation for release builds
- Regularly update dependencies for security patches
- Test on multiple devices and Android versions

## 📈 Monitoring & Analytics

After deployment, monitor:
- App crashes and ANRs
- Performance metrics
- User engagement with AI features
- Audio playback success rates
- Network request success/failure rates

## 🆘 Support & Maintenance

For ongoing maintenance:
1. Monitor app performance and user feedback
2. Update dependencies regularly
3. Test new Android versions compatibility
4. Backup keystore and signing certificates
5. Keep build scripts and documentation updated

---

## 🎯 Final Build Command

For production-ready APK:
```bash
flutter clean && \
flutter pub get && \
flutter build apk --release \
  --obfuscate \
  --split-debug-info=build/debug-info \
  --tree-shake-icons
```

**Output**: `build/app/outputs/flutter-apk/app-release.apk`

---

*This guide covers the complete build and deployment process for the Mindease mental health app with enhanced audio processing, AI analysis, and mood tracking features.*
