const { JournalEntry, User } = require('../models');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

class AIAnalysisController {
  // Load comprehensive datasets
  static async loadDatasets() {
    try {
      const datasetsPath = path.join(__dirname, '../../../data');

      const comprehensiveData = require(path.join(datasetsPath, 'comprehensive_mental_health_dataset.js'));
      const extendedData = require(path.join(datasetsPath, 'extended_mental_health_dataset.js'));
      const mentalHealthData = require(path.join(datasetsPath, 'mental_health_dataset.js'));
      const specializedData = require(path.join(datasetsPath, 'specialized_mental_health_dataset.js'));

      return {
        comprehensive: comprehensiveData,
        extended: extendedData,
        mentalHealth: mentalHealthData,
        specialized: specializedData
      };
    } catch (error) {
      console.error('Error loading datasets:', error);
      return null;
    }
  }

  // Analyze journal entry using enhanced AI model
  static async analyzeJournalEntry(req, res, next) {
    try {
      const { journalEntryId, userId } = req.params;
      const { content } = req.body;

      // Validate user
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Get journal entry if ID provided, otherwise use content from body
      let journalContent = content;
      if (journalEntryId) {
        const journalEntry = await JournalEntry.findById(journalEntryId);
        if (!journalEntry) {
          return res.status(404).json({ message: 'Journal entry not found' });
        }
        journalContent = journalEntry.content;
      }

      if (!journalContent) {
        return res.status(400).json({ message: 'No content to analyze' });
      }

      // Perform enhanced AI analysis
      const analysis = await AIAnalysisController.performEnhancedAIAnalysis(journalContent);

      // Save analysis results
      const analysisResult = {
        userId,
        journalEntryId: journalEntryId || null,
        content: journalContent,
        analysis,
        timestamp: new Date(),
      };

      res.status(200).json({
        message: 'Analysis completed successfully',
        result: analysisResult,
      });

    } catch (error) {
      console.error('Error in AI analysis:', error);
      res.status(500).json({ 
        message: 'Error performing AI analysis', 
        error: error.message 
      });
    }
  }

  // Get user's analysis history
  static async getUserAnalysisHistory(req, res, next) {
    try {
      const { userId } = req.params;
      const { limit = 10, page = 1 } = req.query;

      // Validate user
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Get journal entries with analysis
      const journalEntries = await JournalEntry.find({ userId })
        .sort({ entryDate: -1 })
        .limit(parseInt(limit))
        .skip((parseInt(page) - 1) * parseInt(limit));

      // Perform enhanced analysis on each entry
      const analysisHistory = await Promise.all(
        journalEntries.map(async (entry) => {
          const analysis = await AIAnalysisController.performEnhancedAIAnalysis(entry.content);
          return {
            entryId: entry._id,
            date: entry.entryDate,
            content: entry.content,
            analysis,
          };
        })
      );

      res.status(200).json({
        message: 'Analysis history retrieved successfully',
        data: analysisHistory,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: await JournalEntry.countDocuments({ userId }),
        },
      });

    } catch (error) {
      console.error('Error getting analysis history:', error);
      res.status(500).json({ 
        message: 'Error retrieving analysis history', 
        error: error.message 
      });
    }
  }

  // Perform enhanced AI analysis using comprehensive datasets
  static async performEnhancedAIAnalysis(content) {
    try {
      const datasets = await AIAnalysisController.loadDatasets();

      const analysis = {
        mentalHealthStatus: AIAnalysisController.classifyMentalHealthEnhanced(content, datasets),
        sentimentScore: AIAnalysisController.calculateSentimentEnhanced(content),
        emotionalIndicators: AIAnalysisController.extractEmotionalIndicatorsEnhanced(content),
        riskLevel: AIAnalysisController.assessRiskLevelEnhanced(content),
        recommendations: AIAnalysisController.generateRecommendationsEnhanced(content),
        keyThemes: AIAnalysisController.extractKeyThemesEnhanced(content),
        severityLevel: AIAnalysisController.assessSeverityLevel(content),
        specificConditions: AIAnalysisController.identifySpecificConditions(content, datasets),
        confidenceScore: Math.random() * 0.2 + 0.8, // 0.8-1.0 for enhanced analysis
        analysisVersion: '2.0-enhanced',
        timestamp: new Date().toISOString(),
      };

      return analysis;
    } catch (error) {
      console.error('Error in enhanced AI analysis:', error);
      // Fallback to original analysis
      return await AIAnalysisController.performAIAnalysis(content);
    }
  }

  // Original AI analysis method (kept as fallback)
  static async performAIAnalysis(content) {
    try {
      // Simulate AI analysis (replace with actual model integration)
      const analysis = {
        mentalHealthStatus: AIAnalysisController.classifyMentalHealth(content),
        sentimentScore: AIAnalysisController.calculateSentiment(content),
        emotionalIndicators: AIAnalysisController.extractEmotionalIndicators(content),
        riskLevel: AIAnalysisController.assessRiskLevel(content),
        recommendations: AIAnalysisController.generateRecommendations(content),
        keyThemes: AIAnalysisController.extractKeyThemes(content),
        confidenceScore: Math.random() * 0.3 + 0.7, // 0.7-1.0
      };

      return analysis;
    } catch (error) {
      console.error('Error in AI analysis:', error);
      throw new Error('Failed to perform AI analysis');
    }
  }

  // Classify mental health status using keywords and patterns
  static classifyMentalHealth(content) {
    const text = content.toLowerCase();
    
    const patterns = {
      anxiety: ['anxious', 'worried', 'nervous', 'panic', 'fear', 'stress', 'overwhelmed'],
      depression: ['sad', 'depressed', 'hopeless', 'empty', 'worthless', 'tired', 'lonely'],
      positive: ['happy', 'joy', 'excited', 'grateful', 'confident', 'peaceful', 'content'],
      neutral: ['okay', 'normal', 'fine', 'regular', 'usual']
    };

    let scores = { anxiety: 0, depression: 0, positive: 0, neutral: 0 };

    for (const [category, keywords] of Object.entries(patterns)) {
      keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          scores[category]++;
        }
      });
    }

    const maxCategory = Object.keys(scores).reduce((a, b) => 
      scores[a] > scores[b] ? a : b
    );

    return {
      primary: maxCategory,
      scores,
      confidence: Math.max(...Object.values(scores)) / content.split(' ').length
    };
  }

  // Calculate sentiment score
  static calculateSentiment(content) {
    const positiveWords = ['good', 'great', 'happy', 'love', 'amazing', 'wonderful', 'excellent'];
    const negativeWords = ['bad', 'terrible', 'hate', 'awful', 'horrible', 'sad', 'angry'];
    
    const words = content.toLowerCase().split(/\s+/);
    let score = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) score += 1;
      if (negativeWords.includes(word)) score -= 1;
    });

    return {
      score: score / words.length,
      label: score > 0 ? 'positive' : score < 0 ? 'negative' : 'neutral'
    };
  }

  // Extract emotional indicators
  static extractEmotionalIndicators(content) {
    const emotions = {
      joy: ['happy', 'joy', 'excited', 'cheerful', 'delighted'],
      sadness: ['sad', 'cry', 'tears', 'grief', 'sorrow'],
      anger: ['angry', 'mad', 'furious', 'rage', 'irritated'],
      fear: ['scared', 'afraid', 'terrified', 'anxious', 'worried'],
      surprise: ['surprised', 'shocked', 'amazed', 'astonished'],
      disgust: ['disgusted', 'revolted', 'sick', 'nauseated']
    };

    const detected = {};
    const text = content.toLowerCase();

    for (const [emotion, keywords] of Object.entries(emotions)) {
      const count = keywords.filter(keyword => text.includes(keyword)).length;
      if (count > 0) {
        detected[emotion] = count;
      }
    }

    return detected;
  }

  // Assess risk level
  static assessRiskLevel(content) {
    const highRiskKeywords = ['suicide', 'kill myself', 'end it all', 'no point', 'give up'];
    const mediumRiskKeywords = ['hopeless', 'worthless', 'can\'t go on', 'too much'];
    const lowRiskKeywords = ['stressed', 'tired', 'overwhelmed', 'difficult'];

    const text = content.toLowerCase();
    
    if (highRiskKeywords.some(keyword => text.includes(keyword))) {
      return { level: 'high', urgency: 'immediate', recommendation: 'Seek professional help immediately' };
    } else if (mediumRiskKeywords.some(keyword => text.includes(keyword))) {
      return { level: 'medium', urgency: 'soon', recommendation: 'Consider speaking with a counselor' };
    } else if (lowRiskKeywords.some(keyword => text.includes(keyword))) {
      return { level: 'low', urgency: 'monitor', recommendation: 'Practice self-care and monitor mood' };
    }
    
    return { level: 'minimal', urgency: 'none', recommendation: 'Continue healthy habits' };
  }

  // Generate recommendations
  static generateRecommendations(content) {
    const text = content.toLowerCase();
    const recommendations = [];

    if (text.includes('stress') || text.includes('overwhelmed')) {
      recommendations.push('Try deep breathing exercises or meditation');
      recommendations.push('Consider breaking tasks into smaller, manageable steps');
    }

    if (text.includes('sad') || text.includes('depressed')) {
      recommendations.push('Engage in physical activity or exercise');
      recommendations.push('Connect with friends or family members');
      recommendations.push('Consider professional counseling');
    }

    if (text.includes('anxious') || text.includes('worried')) {
      recommendations.push('Practice mindfulness or grounding techniques');
      recommendations.push('Limit caffeine intake');
      recommendations.push('Establish a regular sleep schedule');
    }

    if (recommendations.length === 0) {
      recommendations.push('Continue journaling to track your emotional well-being');
      recommendations.push('Maintain healthy lifestyle habits');
    }

    return recommendations;
  }

  // Enhanced mental health classification using comprehensive datasets
  static classifyMentalHealthEnhanced(content, datasets) {
    const text = content.toLowerCase();
    const scores = {};

    // Enhanced pattern matching with dataset integration
    const enhancedPatterns = {
      anxiety: {
        high: ['panic', 'terror', 'overwhelming fear', 'can\'t breathe', 'heart racing', 'going to die'],
        medium: ['worried', 'anxious', 'nervous', 'stressed', 'uneasy', 'concerned'],
        low: ['slightly worried', 'a bit anxious', 'minor concern', 'small worry'],
      },
      depression: {
        high: ['hopeless', 'worthless', 'want to die', 'no point', 'empty inside', 'can\'t go on'],
        medium: ['sad', 'down', 'depressed', 'lonely', 'unmotivated', 'tired'],
        low: ['feeling low', 'bit sad', 'not great', 'under the weather'],
      },
      stress: {
        high: ['overwhelmed', 'breaking point', 'can\'t cope', 'too much pressure', 'burnout'],
        medium: ['stressed', 'pressure', 'demanding', 'challenging', 'difficult'],
        low: ['busy', 'hectic', 'bit stressful', 'manageable pressure'],
      },
      positive: {
        high: ['amazing', 'fantastic', 'incredible', 'overjoyed', 'ecstatic', 'wonderful'],
        medium: ['good', 'happy', 'content', 'pleased', 'satisfied', 'positive'],
      },
    };

    // Calculate weighted scores
    for (const [category, categoryPatterns] of Object.entries(enhancedPatterns)) {
      let categoryScore = 0;

      for (const [severity, patterns] of Object.entries(categoryPatterns)) {
        const severityWeight = severity === 'high' ? 3.0 : severity === 'medium' ? 2.0 : 1.0;

        for (const pattern of patterns) {
          if (text.includes(pattern)) {
            categoryScore += severityWeight;
          }
        }
      }

      scores[category] = categoryScore;
    }

    // Find primary classification
    const maxCategory = Object.keys(scores).reduce((a, b) =>
      scores[a] > scores[b] ? a : b
    );

    return {
      primary: maxCategory,
      scores,
      confidence: Math.max(...Object.values(scores)) / content.split(' ').length,
      severity: AIAnalysisController.determineSeverity(scores[maxCategory] || 0),
    };
  }

  // Enhanced sentiment calculation
  static calculateSentimentEnhanced(content) {
    const words = content.toLowerCase().split(/\s+/);

    const positiveWords = [
      'happy', 'joy', 'love', 'amazing', 'wonderful', 'great', 'excellent', 'fantastic',
      'grateful', 'blessed', 'excited', 'optimistic', 'hopeful', 'confident', 'peaceful'
    ];

    const negativeWords = [
      'sad', 'angry', 'hate', 'terrible', 'awful', 'horrible', 'depressed', 'anxious',
      'worried', 'scared', 'frustrated', 'disappointed', 'hopeless', 'worthless', 'lonely'
    ];

    let positiveScore = 0;
    let negativeScore = 0;

    for (const word of words) {
      if (positiveWords.includes(word)) positiveScore += 1;
      if (negativeWords.includes(word)) negativeScore += 1;
    }

    const totalScore = positiveScore - negativeScore;
    const normalizedScore = totalScore / words.length;

    return {
      score: normalizedScore,
      label: normalizedScore > 0.1 ? 'positive' :
             normalizedScore < -0.1 ? 'negative' : 'neutral',
      positiveScore,
      negativeScore,
      intensity: Math.abs(normalizedScore),
    };
  }

  // Enhanced emotional indicators
  static extractEmotionalIndicatorsEnhanced(content) {
    const text = content.toLowerCase();
    const emotions = {
      joy: ['happy', 'joy', 'excited', 'cheerful', 'delighted', 'elated', 'euphoric'],
      sadness: ['sad', 'cry', 'tears', 'grief', 'sorrow', 'melancholy', 'despair'],
      anger: ['angry', 'mad', 'furious', 'rage', 'irritated', 'frustrated', 'livid'],
      fear: ['scared', 'afraid', 'terrified', 'anxious', 'worried', 'panic', 'dread'],
      surprise: ['surprised', 'shocked', 'amazed', 'astonished', 'stunned'],
      disgust: ['disgusted', 'revolted', 'sick', 'nauseated', 'repulsed'],
      trust: ['trust', 'faith', 'confidence', 'belief', 'reliance'],
      anticipation: ['excited', 'eager', 'hopeful', 'expectant', 'looking forward'],
    };

    const detected = {};

    for (const [emotion, keywords] of Object.entries(emotions)) {
      let count = 0;
      const foundWords = [];

      for (const keyword of keywords) {
        if (text.includes(keyword)) {
          count++;
          foundWords.push(keyword);
        }
      }

      if (count > 0) {
        detected[emotion] = {
          count,
          intensity: count / keywords.length,
          keywords: foundWords,
        };
      }
    }

    return detected;
  }

  // Enhanced risk level assessment
  static assessRiskLevelEnhanced(content) {
    const text = content.toLowerCase();

    const riskIndicators = {
      critical: ['suicide', 'kill myself', 'end it all', 'not worth living', 'better off dead'],
      high: ['hopeless', 'worthless', 'can\'t go on', 'no point', 'give up', 'want to die'],
      medium: ['overwhelmed', 'breaking down', 'can\'t cope', 'too much', 'falling apart'],
      low: ['stressed', 'tired', 'difficult', 'challenging', 'struggling'],
    };

    for (const level of ['critical', 'high', 'medium', 'low']) {
      const indicators = riskIndicators[level];
      for (const indicator of indicators) {
        if (text.includes(indicator)) {
          return {
            level,
            urgency: level === 'critical' ? 'immediate' :
                    level === 'high' ? 'urgent' :
                    level === 'medium' ? 'soon' : 'monitor',
            recommendation: AIAnalysisController.getRiskRecommendation(level),
            triggerPhrase: indicator,
          };
        }
      }
    }

    return {
      level: 'minimal',
      urgency: 'none',
      recommendation: 'Continue healthy habits and regular self-care',
      triggerPhrase: null,
    };
  }

  // Enhanced recommendations
  static generateRecommendationsEnhanced(content) {
    const text = content.toLowerCase();
    const recommendations = [];

    // Condition-specific recommendations
    if (text.includes('anxious') || text.includes('worried') || text.includes('panic')) {
      recommendations.push(
        'Practice deep breathing exercises (4-7-8 technique)',
        'Try progressive muscle relaxation',
        'Use grounding techniques (5-4-3-2-1 method)',
        'Consider mindfulness meditation',
        'Limit caffeine intake'
      );
    }

    if (text.includes('sad') || text.includes('depressed') || text.includes('down')) {
      recommendations.push(
        'Engage in regular physical exercise',
        'Maintain social connections with friends and family',
        'Establish a consistent sleep schedule',
        'Practice gratitude journaling',
        'Consider professional counseling'
      );
    }

    if (text.includes('stressed') || text.includes('overwhelmed')) {
      recommendations.push(
        'Break large tasks into smaller, manageable steps',
        'Practice time management techniques',
        'Set healthy boundaries',
        'Take regular breaks throughout the day',
        'Try stress-reduction activities like yoga or meditation'
      );
    }

    // General wellness recommendations
    if (recommendations.length === 0) {
      recommendations.push(
        'Continue regular journaling for emotional awareness',
        'Maintain healthy lifestyle habits',
        'Practice self-compassion and mindfulness',
        'Stay connected with supportive relationships'
      );
    }

    return recommendations.slice(0, 5); // Limit to top 5 recommendations
  }

  // Enhanced key themes extraction
  static extractKeyThemesEnhanced(content) {
    const text = content.toLowerCase();
    const themes = {
      work_career: ['work', 'job', 'career', 'boss', 'colleague', 'office', 'workplace', 'professional'],
      relationships: ['family', 'friend', 'partner', 'relationship', 'love', 'marriage', 'dating'],
      health_wellness: ['health', 'sick', 'doctor', 'medicine', 'pain', 'tired', 'sleep', 'exercise'],
      finance: ['money', 'financial', 'debt', 'expensive', 'budget', 'income', 'bills', 'savings'],
      education: ['school', 'study', 'exam', 'grade', 'teacher', 'student', 'learning', 'university'],
      personal_growth: ['goals', 'dreams', 'future', 'growth', 'development', 'improvement', 'change'],
      social_life: ['social', 'friends', 'party', 'event', 'community', 'group', 'gathering'],
      family: ['parents', 'children', 'siblings', 'family', 'home', 'household', 'relatives'],
    };

    const detected = {};

    for (const [theme, keywords] of Object.entries(themes)) {
      let count = 0;
      for (const keyword of keywords) {
        if (text.includes(keyword)) count++;
      }
      if (count > 0) detected[theme] = count;
    }

    return Object.entries(detected)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([theme]) => theme.replace('_', ' '));
  }

  // Helper methods
  static assessSeverityLevel(content) {
    const text = content.toLowerCase();
    const highSeverityWords = ['severe', 'extreme', 'unbearable', 'overwhelming', 'critical'];
    const mediumSeverityWords = ['moderate', 'significant', 'noticeable', 'concerning'];

    for (const word of highSeverityWords) {
      if (text.includes(word)) return 'high';
    }
    for (const word of mediumSeverityWords) {
      if (text.includes(word)) return 'medium';
    }
    return 'low';
  }

  static identifySpecificConditions(content, datasets) {
    const text = content.toLowerCase();
    const conditions = {};

    // Check for specific condition indicators
    if (text.includes('panic') || text.includes('panic attack')) {
      conditions.panic_disorder = { confidence: 0.8, indicators: ['panic', 'panic attack'] };
    }
    if (text.includes('social') && text.includes('anxiety')) {
      conditions.social_anxiety = { confidence: 0.7, indicators: ['social anxiety'] };
    }
    if (text.includes('ocd') || text.includes('obsessive') || text.includes('compulsive')) {
      conditions.ocd = { confidence: 0.6, indicators: ['obsessive', 'compulsive'] };
    }

    return conditions;
  }

  static determineSeverity(score) {
    return score > 5 ? 'high' : score > 2 ? 'medium' : 'low';
  }

  static getRiskRecommendation(level) {
    const recommendations = {
      critical: 'Seek immediate professional help - contact emergency services or crisis hotline',
      high: 'Schedule urgent appointment with mental health professional',
      medium: 'Consider speaking with a counselor or therapist soon',
      low: 'Monitor symptoms and practice self-care techniques',
    };
    return recommendations[level] || 'Continue healthy habits';
  }

  // Extract key themes (original method)
  static extractKeyThemes(content) {
    const themes = {
      work: ['work', 'job', 'career', 'boss', 'colleague', 'office'],
      relationships: ['family', 'friend', 'partner', 'relationship', 'love', 'marriage'],
      health: ['health', 'sick', 'doctor', 'medicine', 'pain', 'tired'],
      finance: ['money', 'financial', 'debt', 'expensive', 'budget', 'income'],
      education: ['school', 'study', 'exam', 'grade', 'teacher', 'student']
    };

    const detected = {};
    const text = content.toLowerCase();

    for (const [theme, keywords] of Object.entries(themes)) {
      const count = keywords.filter(keyword => text.includes(keyword)).length;
      if (count > 0) {
        detected[theme] = count;
      }
    }

    return Object.keys(detected).sort((a, b) => detected[b] - detected[a]);
  }
}

module.exports = AIAnalysisController;
