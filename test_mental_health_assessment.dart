import 'package:flutter/material.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/mental_health_assessment_screen.dart';
import 'package:myapp/services/mental_health_assessment_service.dart';
import 'package:myapp/services/assessment_questions_service.dart';
import 'package:myapp/services/therapist_referral_service.dart';
import 'package:myapp/models/assessment_models.dart';

void main() {
  runApp(MentalHealthAssessmentTestApp());
}

class MentalHealthAssessmentTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Mental Health Assessment Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: AssessmentTestScreen(),
    );
  }
}

class AssessmentTestScreen extends StatefulWidget {
  @override
  _AssessmentTestScreenState createState() => _AssessmentTestScreenState();
}

class _AssessmentTestScreenState extends State<AssessmentTestScreen> {
  final AssessmentQuestionsService _questionsService = AssessmentQuestionsService();
  final MentalHealthAssessmentService _assessmentService = MentalHealthAssessmentService();
  final TherapistReferralService _referralService = TherapistReferralService();
  
  bool _isLoading = false;
  String _testResults = '';

  @override
  void initState() {
    super.initState();
    _runComprehensiveTest();
  }

  Future<void> _runComprehensiveTest() async {
    setState(() {
      _isLoading = true;
      _testResults = 'Starting comprehensive mental health assessment test...\n\n';
    });

    try {
      // Test 1: Questions Service
      await _testQuestionsService();
      
      // Test 2: Assessment Service
      await _testAssessmentService();
      
      // Test 3: Therapist Referral Service
      await _testTherapistReferralService();
      
      // Test 4: Integration Test
      await _testFullIntegration();
      
      setState(() {
        _testResults += '\n✅ ALL TESTS PASSED SUCCESSFULLY!\n';
        _testResults += '\n🎉 The Mental Health Assessment System is ready for use!';
      });

    } catch (e) {
      setState(() {
        _testResults += '\n❌ TEST FAILED: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testQuestionsService() async {
    setState(() {
      _testResults += '🧪 Testing Questions Service...\n';
    });

    // Test getting all questions
    final allQuestions = _questionsService.getAllQuestions();
    setState(() {
      _testResults += '  ✓ Total questions loaded: ${allQuestions.length}\n';
    });

    // Test getting questions by category
    final categories = _questionsService.getCategories();
    setState(() {
      _testResults += '  ✓ Categories available: ${categories.join(', ')}\n';
    });

    for (final category in categories) {
      final categoryQuestions = _questionsService.getQuestionsByCategory(category);
      final categoryInfo = _questionsService.getCategoryInfo(category);
      setState(() {
        _testResults += '    - ${categoryInfo['name']}: ${categoryQuestions.length} questions\n';
      });
    }

    setState(() {
      _testResults += '✅ Questions Service test completed\n\n';
    });
  }

  Future<void> _testAssessmentService() async {
    setState(() {
      _testResults += '🧪 Testing Assessment Service...\n';
    });

    // Initialize the service
    await _assessmentService.initialize();
    setState(() {
      _testResults += '  ✓ Assessment service initialized\n';
    });

    // Create sample responses
    final sampleResponses = _createSampleResponses();
    setState(() {
      _testResults += '  ✓ Created ${sampleResponses.length} sample responses\n';
    });

    // Process assessment
    final result = await _assessmentService.processAssessment(sampleResponses);
    setState(() {
      _testResults += '  ✓ Assessment processed successfully\n';
      _testResults += '    - Primary condition: ${result.overallResult.primaryCondition}\n';
      _testResults += '    - Risk level: ${result.riskLevel}\n';
      _testResults += '    - Confidence: ${(result.confidenceScore * 100).toInt()}%\n';
      _testResults += '    - Recommendations: ${result.recommendations.length}\n';
    });

    setState(() {
      _testResults += '✅ Assessment Service test completed\n\n';
    });
  }

  Future<void> _testTherapistReferralService() async {
    setState(() {
      _testResults += '🧪 Testing Therapist Referral Service...\n';
    });

    // Create a mock assessment result
    final mockResult = _createMockAssessmentResult();
    
    // Test therapist recommendations
    final therapists = _referralService.getTherapistRecommendations(mockResult);
    setState(() {
      _testResults += '  ✓ Therapist recommendations: ${therapists.length}\n';
    });

    // Test emergency resources
    final emergencyResources = _referralService.getEmergencyResources();
    setState(() {
      _testResults += '  ✓ Emergency resources: ${emergencyResources.length}\n';
    });

    // Test coping strategies
    final copingStrategies = _referralService.getCopingStrategies(mockResult);
    setState(() {
      _testResults += '  ✓ Coping strategies: ${copingStrategies.length}\n';
    });

    // Test next steps
    final nextSteps = _referralService.getNextSteps(mockResult);
    setState(() {
      _testResults += '  ✓ Next steps: ${nextSteps.length}\n';
    });

    setState(() {
      _testResults += '✅ Therapist Referral Service test completed\n\n';
    });
  }

  Future<void> _testFullIntegration() async {
    setState(() {
      _testResults += '🧪 Testing Full Integration...\n';
    });

    // Test the complete flow
    final questions = _questionsService.getAllQuestions();
    final responses = _createSampleResponses();
    final result = await _assessmentService.processAssessment(responses);
    final therapists = _referralService.getTherapistRecommendations(result);
    
    setState(() {
      _testResults += '  ✓ Complete flow test successful\n';
      _testResults += '    Questions → Responses → Assessment → Referrals\n';
      _testResults += '    ${questions.length} → ${responses.length} → ${result.categoryResults.length} categories → ${therapists.length} referrals\n';
    });

    setState(() {
      _testResults += '✅ Full Integration test completed\n\n';
    });
  }

  List<AssessmentResponse> _createSampleResponses() {
    final questions = _questionsService.getAllQuestions();
    final responses = <AssessmentResponse>[];
    
    // Create responses for first few questions of each category
    final categories = _questionsService.getCategories();
    
    for (final category in categories) {
      final categoryQuestions = _questionsService.getQuestionsByCategory(category);
      
      for (int i = 0; i < categoryQuestions.length && i < 2; i++) {
        final question = categoryQuestions[i];
        final option = question.options[1]; // Select second option (mild severity)
        
        responses.add(AssessmentResponse(
          questionId: question.id,
          optionId: option.id,
          score: option.score * question.weight,
          category: question.category,
          timestamp: DateTime.now(),
        ));
      }
    }
    
    return responses;
  }

  AssessmentResult _createMockAssessmentResult() {
    return AssessmentResult(
      id: 'test_assessment_123',
      completedAt: DateTime.now(),
      categoryResults: {
        'anxiety': CategoryResult(
          category: 'anxiety',
          totalScore: 15,
          maxScore: 30,
          percentage: 50.0,
          severity: 'moderate',
          description: 'Moderate anxiety symptoms detected',
          symptoms: ['Nervousness', 'Worry', 'Restlessness'],
          recommendations: ['Practice deep breathing', 'Consider therapy'],
        ),
      },
      overallResult: OverallResult(
        primaryCondition: 'anxiety',
        secondaryCondition: 'stress',
        overallSeverity: 'moderate',
        summary: 'Assessment indicates moderate anxiety symptoms',
        keyFindings: ['Moderate anxiety detected', 'Stress-related symptoms present'],
        needsImmediateAttention: false,
      ),
      recommendations: [
        'Practice relaxation techniques',
        'Consider professional counseling',
        'Maintain regular exercise',
      ],
      resources: [
        'Anxiety and Depression Association',
        'Mental Health America',
        'Crisis Text Line',
      ],
      requiresProfessionalHelp: false,
      riskLevel: 'medium',
      confidenceScore: 0.85,
      aiAnalysis: {
        'mentalHealthStatus': {'primary': 'anxiety', 'confidence': 0.8},
        'riskLevel': {'level': 'medium'},
        'sentimentScore': 0.3,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppTheme.backgroundGradient),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(child: _buildTestResults()),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppTheme.radiusXL),
          bottomRight: Radius.circular(AppTheme.radiusXL),
        ),
        boxShadow: AppTheme.mediumShadow,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            child: const Icon(Icons.science, color: Colors.white, size: 24),
          ),
          const SizedBox(width: AppTheme.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Assessment System Test',
                  style: AppTheme.headingMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Comprehensive testing suite',
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          if (_isLoading)
            const CircularProgressIndicator(color: Colors.white),
        ],
      ),
    );
  }

  Widget _buildTestResults() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: SingleChildScrollView(
        child: Text(
          _testResults.isEmpty ? 'Test results will appear here...' : _testResults,
          style: const TextStyle(
            fontFamily: 'monospace',
            fontSize: 14,
            height: 1.4,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading ? null : () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const MentalHealthAssessmentScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.assessment),
                  const SizedBox(width: AppTheme.spacingS),
                  Text(
                    'Open Assessment Screen',
                    style: AppTheme.bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _isLoading ? null : _runComprehensiveTest,
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
                side: BorderSide(color: AppTheme.primaryColor),
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
              ),
              child: const Text('Run Tests Again'),
            ),
          ),
        ],
      ),
    );
  }
}

// Instructions to run this test:
// 1. Save this file as test_mental_health_assessment.dart in the lib folder
// 2. Run: flutter run test_mental_health_assessment.dart
// 3. This will test all components of the mental health assessment system
// 4. Check the results and then try the actual assessment screen
