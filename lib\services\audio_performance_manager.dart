import 'dart:async';
import 'dart:isolate';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'audio_processing_service.dart';
import 'audio_enhancement_service.dart';

class AudioPerformanceManager {
  static final AudioPerformanceManager _instance = AudioPerformanceManager._internal();
  factory AudioPerformanceManager() => _instance;
  AudioPerformanceManager._internal();

  final Map<String, Uint8List> _preloadedAudio = {};
  final Map<String, DateTime> _lastAccessed = {};
  final Set<String> _currentlyLoading = {};
  Timer? _memoryCleanupTimer;
  
  // Performance settings
  static const int maxCacheSize = 50 * 1024 * 1024; // 50MB
  static const int maxCachedFiles = 10;
  static const Duration cacheExpiryTime = Duration(minutes: 30);
  static const Duration cleanupInterval = Duration(minutes: 5);

  /// Initialize the performance manager
  void initialize() {
    _startMemoryCleanup();
    _preloadPopularSounds();
    debugPrint('🚀 Audio Performance Manager initialized');
  }

  /// Preload popular/frequently used sounds
  Future<void> _preloadPopularSounds() async {
    final popularSounds = [
      'Rain Sounds',
      'Ocean Waves',
      'Forest Sounds',
      'White Noise',
    ];

    for (final sound in popularSounds) {
      unawaited(_preloadAudio(sound));
    }
  }

  /// Preload audio file in background
  Future<void> _preloadAudio(String soundName) async {
    if (_preloadedAudio.containsKey(soundName) || _currentlyLoading.contains(soundName)) {
      return;
    }

    _currentlyLoading.add(soundName);
    
    try {
      final audioService = AudioProcessingService();
      final audioData = await audioService.loadAudioFile(soundName);
      
      if (audioData != null) {
        _preloadedAudio[soundName] = audioData;
        _lastAccessed[soundName] = DateTime.now();
        debugPrint('✅ Preloaded audio: $soundName (${audioData.length} bytes)');
        
        // Check cache size and cleanup if needed
        _checkCacheSize();
      }
    } catch (e) {
      debugPrint('❌ Error preloading audio $soundName: $e');
    } finally {
      _currentlyLoading.remove(soundName);
    }
  }

  /// Get audio data with performance optimization
  Future<Uint8List?> getOptimizedAudio(String soundName, {EnhancementSettings? settings}) async {
    // Update access time
    _lastAccessed[soundName] = DateTime.now();
    
    // Check if already cached
    if (_preloadedAudio.containsKey(soundName)) {
      debugPrint('🎯 Cache hit for: $soundName');
      
      if (settings != null) {
        // Apply enhancements in background isolate for better performance
        return await _processInBackground(soundName, _preloadedAudio[soundName]!, settings);
      }
      
      return _preloadedAudio[soundName];
    }

    // Load and cache if not available
    debugPrint('💾 Cache miss for: $soundName, loading...');
    await _preloadAudio(soundName);
    
    if (_preloadedAudio.containsKey(soundName)) {
      if (settings != null) {
        return await _processInBackground(soundName, _preloadedAudio[soundName]!, settings);
      }
      return _preloadedAudio[soundName];
    }
    
    return null;
  }

  /// Process audio in background isolate for better performance
  Future<Uint8List> _processInBackground(String soundName, Uint8List audioData, EnhancementSettings settings) async {
    try {
      // For now, return original data as background processing requires more complex setup
      // In a real implementation, you would:
      // 1. Spawn an isolate
      // 2. Send audio data and settings to isolate
      // 3. Process audio in isolate
      // 4. Return processed data
      
      debugPrint('🔧 Processing audio in background: $soundName');
      
      // Simulate background processing delay
      await Future.delayed(const Duration(milliseconds: 50));
      
      return audioData;
    } catch (e) {
      debugPrint('❌ Error processing audio in background: $e');
      return audioData;
    }
  }

  /// Start periodic memory cleanup
  void _startMemoryCleanup() {
    _memoryCleanupTimer = Timer.periodic(cleanupInterval, (_) {
      _performMemoryCleanup();
    });
  }

  /// Perform memory cleanup
  void _performMemoryCleanup() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    // Find expired entries
    for (final entry in _lastAccessed.entries) {
      if (now.difference(entry.value) > cacheExpiryTime) {
        expiredKeys.add(entry.key);
      }
    }
    
    // Remove expired entries
    for (final key in expiredKeys) {
      _preloadedAudio.remove(key);
      _lastAccessed.remove(key);
      debugPrint('🧹 Removed expired audio from cache: $key');
    }
    
    // Check cache size after cleanup
    _checkCacheSize();
    
    if (expiredKeys.isNotEmpty) {
      debugPrint('🧹 Memory cleanup completed, removed ${expiredKeys.length} expired entries');
    }
  }

  /// Check and manage cache size
  void _checkCacheSize() {
    // Check file count limit
    if (_preloadedAudio.length > maxCachedFiles) {
      _removeLeastRecentlyUsed();
    }
    
    // Check memory size limit
    final totalSize = _preloadedAudio.values.fold(0, (sum, data) => sum + data.length);
    if (totalSize > maxCacheSize) {
      _removeLeastRecentlyUsed();
    }
  }

  /// Remove least recently used items
  void _removeLeastRecentlyUsed() {
    if (_lastAccessed.isEmpty) return;
    
    // Sort by last accessed time
    final sortedEntries = _lastAccessed.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    // Remove oldest entries until we're under limits
    final toRemove = (sortedEntries.length * 0.3).ceil(); // Remove 30% of oldest
    
    for (int i = 0; i < toRemove && i < sortedEntries.length; i++) {
      final key = sortedEntries[i].key;
      _preloadedAudio.remove(key);
      _lastAccessed.remove(key);
      debugPrint('🧹 Removed LRU audio from cache: $key');
    }
  }

  /// Get cache statistics
  CacheStatistics getCacheStatistics() {
    final totalSize = _preloadedAudio.values.fold(0, (sum, data) => sum + data.length);
    
    return CacheStatistics(
      cachedFiles: _preloadedAudio.length,
      totalSizeBytes: totalSize,
      totalSizeMB: totalSize / (1024 * 1024),
      hitRate: _calculateHitRate(),
      memoryUsagePercent: (totalSize / maxCacheSize) * 100,
    );
  }

  /// Calculate cache hit rate (simplified)
  double _calculateHitRate() {
    // In a real implementation, you would track hits and misses
    return _preloadedAudio.isNotEmpty ? 0.85 : 0.0; // Simulated 85% hit rate
  }

  /// Warm up cache with specific sounds
  Future<void> warmUpCache(List<String> soundNames) async {
    debugPrint('🔥 Warming up cache with ${soundNames.length} sounds');
    
    for (final soundName in soundNames) {
      unawaited(_preloadAudio(soundName));
    }
  }

  /// Clear all cached audio
  void clearCache() {
    _preloadedAudio.clear();
    _lastAccessed.clear();
    debugPrint('🧹 Audio cache cleared completely');
  }

  /// Dispose resources
  void dispose() {
    _memoryCleanupTimer?.cancel();
    clearCache();
    debugPrint('🧹 Audio Performance Manager disposed');
  }
}

class CacheStatistics {
  final int cachedFiles;
  final int totalSizeBytes;
  final double totalSizeMB;
  final double hitRate;
  final double memoryUsagePercent;

  CacheStatistics({
    required this.cachedFiles,
    required this.totalSizeBytes,
    required this.totalSizeMB,
    required this.hitRate,
    required this.memoryUsagePercent,
  });

  @override
  String toString() {
    return 'CacheStats(files: $cachedFiles, size: ${totalSizeMB.toStringAsFixed(1)}MB, hitRate: ${(hitRate * 100).toStringAsFixed(1)}%, usage: ${memoryUsagePercent.toStringAsFixed(1)}%)';
  }
}

/// Helper function for fire-and-forget futures
void unawaited(Future<void> future) {
  future.catchError((error) {
    debugPrint('❌ Unawaited future error: $error');
  });
}
