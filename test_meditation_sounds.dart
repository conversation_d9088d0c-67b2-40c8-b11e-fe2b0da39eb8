import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/services/audio_processing_service.dart';
import 'package:myapp/services/enhanced_audio_player_service.dart';
import 'package:myapp/meditation_screen.dart';

void main() {
  runApp(MeditationSoundsTestApp());
}

class MeditationSoundsTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Meditation Sounds Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: SoundsTestScreen(),
    );
  }
}

class SoundsTestScreen extends StatefulWidget {
  @override
  _SoundsTestScreenState createState() => _SoundsTestScreenState();
}

class _SoundsTestScreenState extends State<SoundsTestScreen> {
  final AudioProcessingService _audioService = AudioProcessingService();
  final EnhancedAudioPlayerService _playerService = EnhancedAudioPlayerService();
  
  bool _isLoading = false;
  String _testResults = '';
  List<String> _availableSounds = [];
  String? _currentlyTesting;

  @override
  void initState() {
    super.initState();
    _runSoundsTest();
  }

  Future<void> _runSoundsTest() async {
    setState(() {
      _isLoading = true;
      _testResults = 'Starting meditation sounds test...\n\n';
    });

    try {
      // Test 1: Check available sounds
      await _testAvailableSounds();
      
      // Test 2: Check asset accessibility
      await _testAssetAccessibility();
      
      // Test 3: Test audio player service
      await _testAudioPlayerService();
      
      setState(() {
        _testResults += '\n✅ ALL MEDITATION SOUNDS TESTS PASSED!\n';
        _testResults += '\n🎵 All ${_availableSounds.length} meditation sounds are properly configured and accessible!';
      });

    } catch (e) {
      setState(() {
        _testResults += '\n❌ TEST FAILED: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testAvailableSounds() async {
    setState(() {
      _testResults += '🧪 Testing Available Sounds...\n';
    });

    _availableSounds = _audioService.getAvailableSounds();
    
    setState(() {
      _testResults += '  ✓ Found ${_availableSounds.length} available sounds:\n';
      for (final sound in _availableSounds) {
        _testResults += '    - $sound\n';
      }
      _testResults += '\n';
    });
  }

  Future<void> _testAssetAccessibility() async {
    setState(() {
      _testResults += '🧪 Testing Asset Accessibility...\n';
    });

    int accessibleCount = 0;
    int failedCount = 0;

    for (final soundName in _availableSounds) {
      setState(() {
        _currentlyTesting = soundName;
      });

      try {
        final filePath = AudioProcessingService.soundFileMappings[soundName];
        if (filePath != null) {
          // Try to load the asset to verify it exists
          final data = await rootBundle.load(filePath);
          if (data.lengthInBytes > 0) {
            accessibleCount++;
            setState(() {
              _testResults += '    ✓ $soundName (${(data.lengthInBytes / 1024).toStringAsFixed(1)} KB)\n';
            });
          } else {
            failedCount++;
            setState(() {
              _testResults += '    ❌ $soundName (empty file)\n';
            });
          }
        } else {
          failedCount++;
          setState(() {
            _testResults += '    ❌ $soundName (no file path mapping)\n';
          });
        }
      } catch (e) {
        failedCount++;
        setState(() {
          _testResults += '    ❌ $soundName (error: $e)\n';
        });
      }

      // Small delay to show progress
      await Future.delayed(const Duration(milliseconds: 100));
    }

    setState(() {
      _currentlyTesting = null;
      _testResults += '\n  📊 Results: $accessibleCount accessible, $failedCount failed\n\n';
    });

    if (failedCount > 0) {
      throw Exception('$failedCount sound files are not accessible');
    }
  }

  Future<void> _testAudioPlayerService() async {
    setState(() {
      _testResults += '🧪 Testing Audio Player Service...\n';
    });

    try {
      // Initialize the audio player service
      await _playerService.initialize();
      setState(() {
        _testResults += '  ✓ Audio player service initialized\n';
      });

      // Test loading a sound (without actually playing it)
      if (_availableSounds.isNotEmpty) {
        final testSound = _availableSounds.first;
        setState(() {
          _testResults += '  ✓ Testing sound loading with: $testSound\n';
        });

        // Test if the audio service can find the file path
        final filePath = AudioProcessingService.soundFileMappings[testSound];
        if (filePath != null) {
          setState(() {
            _testResults += '  ✓ File path resolved: $filePath\n';
          });
        } else {
          throw Exception('No file path found for $testSound');
        }
      }

      setState(() {
        _testResults += '  ✓ Audio player service test completed\n\n';
      });

    } catch (e) {
      setState(() {
        _testResults += '  ❌ Audio player service error: $e\n\n';
      });
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppTheme.backgroundGradient),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(child: _buildTestResults()),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppTheme.radiusXL),
          bottomRight: Radius.circular(AppTheme.radiusXL),
        ),
        boxShadow: AppTheme.mediumShadow,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            child: const Icon(Icons.music_note, color: Colors.white, size: 24),
          ),
          const SizedBox(width: AppTheme.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Meditation Sounds Test',
                  style: AppTheme.headingMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _currentlyTesting != null 
                    ? 'Testing: $_currentlyTesting'
                    : 'Audio assets verification',
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          if (_isLoading)
            const CircularProgressIndicator(color: Colors.white),
        ],
      ),
    );
  }

  Widget _buildTestResults() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingM),
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
      ),
      child: SingleChildScrollView(
        child: Text(
          _testResults.isEmpty ? 'Test results will appear here...' : _testResults,
          style: const TextStyle(
            fontFamily: 'monospace',
            fontSize: 14,
            height: 1.4,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: AppTheme.softShadow,
      ),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading ? null : () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => MeditationScreen(userId: 'test_user'),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.radiusM),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.self_improvement),
                  const SizedBox(width: AppTheme.spacingS),
                  Text(
                    'Open Meditation Screen',
                    style: AppTheme.bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingS),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _isLoading ? null : _runSoundsTest,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                    side: BorderSide(color: AppTheme.primaryColor),
                    padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                  ),
                  child: const Text('Run Test Again'),
                ),
              ),
              const SizedBox(width: AppTheme.spacingS),
              Expanded(
                child: ElevatedButton(
                  onPressed: _availableSounds.isNotEmpty && !_isLoading ? () {
                    _showSoundsList();
                  } : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.secondaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingM),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusM),
                    ),
                  ),
                  child: Text('View Sounds (${_availableSounds.length})'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showSoundsList() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Available Meditation Sounds (${_availableSounds.length})'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: _availableSounds.length,
            itemBuilder: (context, index) {
              final soundName = _availableSounds[index];
              final filePath = AudioProcessingService.soundFileMappings[soundName];
              return ListTile(
                leading: const Icon(Icons.music_note, color: Colors.blue),
                title: Text(soundName),
                subtitle: Text(filePath ?? 'No file path'),
                trailing: const Icon(Icons.check_circle, color: Colors.green),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _playerService.dispose();
    super.dispose();
  }
}

// Instructions to run this test:
// 1. Save this file as test_meditation_sounds.dart in the lib folder
// 2. Run: flutter run test_meditation_sounds.dart
// 3. This will test all meditation sound assets and their accessibility
// 4. Check that all 18 sound files are properly loaded and accessible
// 5. Then test the actual meditation screen to ensure sounds play correctly
